
package org.linphone.notifications

import android.app.NotificationManager
import android.app.RemoteInput
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import org.linphone.LinphoneApplication.Companion.coreContext
import org.linphone.core.Address
import org.linphone.core.ConferenceParams
import org.linphone.core.tools.Log

class NotificationBroadcastReceiver : BroadcastReceiver() {
    companion object {
        private const val TAG = "[Notification Broadcast Receiver]"
    }

    override fun onReceive(context: Context, intent: Intent) {
        val notificationId = intent.getIntExtra(NotificationsManager.INTENT_NOTIF_ID, 0)
        Log.i(
            "$TAG Got notification broadcast for ID [$notificationId]"
        )

        // Wait for coreContext to be ready to handle intent
        while (!coreContext.isReady()) {
            Thread.sleep(50)
        }

        if (intent.action == NotificationsManager.INTENT_ANSWER_CALL_NOTIF_ACTION || intent.action == NotificationsManager.INTENT_HANGUP_CALL_NOTIF_ACTION) {
            handleCallIntent(intent, notificationId)
        } else if (intent.action == NotificationsManager.INTENT_REPLY_MESSAGE_NOTIF_ACTION || intent.action == NotificationsManager.INTENT_MARK_MESSAGE_AS_READ_NOTIF_ACTION) {
            handleChatIntent(context, intent, notificationId)
        }
    }

    private fun handleCallIntent(intent: Intent, notificationId: Int) {
        val remoteSipAddress = intent.getStringExtra(NotificationsManager.INTENT_REMOTE_ADDRESS)
        if (remoteSipAddress == null) {
            Log.e("$TAG Remote SIP address is null for call notification ID [$notificationId]")
            return
        }

        coreContext.postOnCoreThread { core ->
            // Try multiple methods to find the call
            var call = core.calls.find {
                it.remoteAddress.asStringUriOnly() == remoteSipAddress
            }
            
            // If not found, try with weakEqual comparison
            if (call == null) {
                val targetAddress = core.interpretUrl(remoteSipAddress, false)
                if (targetAddress != null) {
                    call = core.calls.find {
                        it.remoteAddress.weakEqual(targetAddress)
                    }
                }
            }
            
            // If still not found, try getCallByRemoteAddress2
            if (call == null) {
                val targetAddress = core.interpretUrl(remoteSipAddress, false)
                if (targetAddress != null) {
                    call = core.getCallByRemoteAddress2(targetAddress)
                }
            }
            
            // If still not found, log all current calls for debugging
            if (call == null) {
                Log.e("$TAG Couldn't find call from remote address [$remoteSipAddress]")
                
                // Check if there's a current call we can use as fallback
                val currentCall = core.currentCall
                if (currentCall != null) {
                    call = currentCall
                } else if (core.callsNb > 0) {
                    call = core.calls[0]
                }
            }
            
            if (call != null) {
                if (intent.action == NotificationsManager.INTENT_ANSWER_CALL_NOTIF_ACTION) {
                    if (call.state == org.linphone.core.Call.State.IncomingReceived || 
                        call.state == org.linphone.core.Call.State.IncomingEarlyMedia
                    ) {
                        coreContext.answerCall(call)
                    } else {
                        // Cancel the notification since we can't answer the call
                        coreContext.notificationsManager.removeIncomingCallNotification()
                        coreContext.notificationsManager.cancelNotification(notificationId)
                        
                        // Force cancel using Android's NotificationManager as backup
                        coreContext.postOnMainThread {
                            val androidNotificationManager = coreContext.context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                            androidNotificationManager.cancel(notificationId)
                            androidNotificationManager.cancel(1) // INCOMING_CALL_ID
                        }
                    }
                } else {
                    coreContext.terminateCall(call)
                }
            } else {
                // Cancel the notification since there's no call to handle
                coreContext.notificationsManager.removeIncomingCallNotification()
                // Also cancel any other call notifications that might be hanging
                coreContext.notificationsManager.cancelNotification(notificationId)
                // Force cancel all call-related notifications
                coreContext.notificationsManager.cleanupAllCallNotifications()
                
                // As a last resort, cancel the notification directly using Android's NotificationManager
                coreContext.postOnMainThread {
                    val androidNotificationManager = coreContext.context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
                    androidNotificationManager.cancel(notificationId)
                    // Also try canceling the standard incoming call ID
                    androidNotificationManager.cancel(1) // INCOMING_CALL_ID
                }
            }
        }
    }

    private fun handleChatIntent(context: Context, intent: Intent, notificationId: Int) {
        val remoteSipAddress = intent.getStringExtra(NotificationsManager.INTENT_REMOTE_ADDRESS)
        if (remoteSipAddress == null) {
            Log.e("$TAG Remote SIP address is null for notification ID [$notificationId]")
            return
        }
        val localIdentity = intent.getStringExtra(NotificationsManager.INTENT_LOCAL_IDENTITY)
        if (localIdentity == null) {
            Log.e("$TAG Local identity is null for notification ID [$notificationId]")
            return
        }

        val reply = getMessageText(intent)?.toString()
        if (intent.action == NotificationsManager.INTENT_REPLY_MESSAGE_NOTIF_ACTION) {
            if (reply == null) {
                Log.e("$TAG Couldn't get reply text")
                return
            }
        }

        coreContext.postOnCoreThread { core ->
            val remoteAddress = core.interpretUrl(remoteSipAddress, false)
            if (remoteAddress == null) {
                Log.e(
                    "$TAG Couldn't interpret remote address [$remoteSipAddress]"
                )
                return@postOnCoreThread
            }

            val localAddress = core.interpretUrl(localIdentity, false)
            if (localAddress == null) {
                Log.e(
                    "$TAG Couldn't interpret local address [$localIdentity]"
                )
                return@postOnCoreThread
            }

            val params: ConferenceParams? = null
            val room = core.searchChatRoom(
                params,
                localAddress,
                remoteAddress,
                arrayOfNulls<Address>(
                    0
                )
            )
            if (room == null) {
                Log.e(
                    "$TAG Couldn't find conversation for remote address [$remoteSipAddress] and local address [$localIdentity]"
                )
                return@postOnCoreThread
            }

            if (intent.action == NotificationsManager.INTENT_REPLY_MESSAGE_NOTIF_ACTION) {
                val msg = room.createMessageFromUtf8(reply)
                msg.userData = notificationId
                msg.addListener(coreContext.notificationsManager.chatMessageListener)
                msg.send()
                Log.i("$TAG Reply sent for notif id [$notificationId]")
            } else if (intent.action == NotificationsManager.INTENT_MARK_MESSAGE_AS_READ_NOTIF_ACTION) {
                Log.i("$TAG Marking chat room from notification id [$notificationId] as read")
                room.markAsRead()
                if (!coreContext.notificationsManager.dismissChatNotification(room)) {
                    Log.w(
                        "$TAG Notifications Manager failed to cancel notification"
                    )
                    val notificationManager = context.getSystemService(
                        NotificationManager::class.java
                    )
                    notificationManager.cancel(NotificationsManager.CHAT_TAG, notificationId)
                }
            }
        }
    }

    private fun getMessageText(intent: Intent): CharSequence? {
        val remoteInput = RemoteInput.getResultsFromIntent(intent)
        return remoteInput?.getCharSequence(NotificationsManager.KEY_TEXT_REPLY)
    }
}
