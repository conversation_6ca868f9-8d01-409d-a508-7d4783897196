
package org.linphone.ui.main.chat.model

import androidx.annotation.UiThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.MutableLiveData
import org.linphone.LinphoneApplication.Companion.coreContext
import org.linphone.LinphoneApplication.Companion.corePreferences
import org.linphone.core.Address

class MessageBottomSheetParticipantModel
    @WorkerThread
    constructor(
    address: Address,
    val value: String,
    val timestamp: Long,
    val isOurOwnReaction: Boolean = false,
    val onClick: (() -> Unit)? = null
) {
    val sipUri = address.asStringUriOnly()

    val showSipUri = MutableLiveData<Boolean>()

    val avatarModel = coreContext.contactsManager.getContactAvatarModelForAddress(address)

    init {
        showSipUri.postValue(false)
    }

    @UiThread
    fun clicked() {
        if (!isOurOwnReaction && !corePreferences.onlyDisplaySipUriUsername) {
            showSipUri.postValue(showSipUri.value == false)
        } else {
            onClick?.invoke()
        }
    }
}
