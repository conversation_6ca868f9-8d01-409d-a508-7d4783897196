
package org.linphone.ui.main.viewmodel

import android.net.Uri
import android.os.Bundle
import androidx.annotation.UiThread
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import org.linphone.core.ChatRoom
import org.linphone.core.ConferenceInfo
import org.linphone.core.Friend
import org.linphone.ui.main.chat.model.MessageModel
import org.linphone.ui.main.recordings.model.RecordingModel
import org.linphone.utils.Event

class SharedMainViewModel
    @UiThread
    constructor() : ViewModel() {
    // Sliding Pane & navigation related

    val isSlidingPaneSlideable = MutableLiveData<Boolean>()

    val closeSlidingPaneEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val openSlidingPaneEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val navigateToHistoryEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val navigateToContactsEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val navigateToConversationsEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val navigateToMeetingsEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    var currentlyDisplayedFragment = MutableLiveData<Int>()

    // Top bar related

    val searchFilter: MutableLiveData<Event<String>> by lazy {
        MutableLiveData<Event<String>>()
    }

    val refreshDrawerMenuAccountsListEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val refreshDrawerMenuQuitButtonEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val forceUpdateAvailableNavigationItems: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    // Contacts related

    var displayedFriend: Friend? = null // Prevents the need to go look for the friend
    val showContactEvent: MutableLiveData<Event<String>> by lazy {
        MutableLiveData<Event<String>>()
    }

    val showNewContactEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    var sipAddressToAddToNewContact: String = ""

    // Call logs related

    val forceRefreshCallLogsListEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val resetMissedCallsCountEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    // Conversation related

    val textToShareFromIntent = MutableLiveData<String>()

    val filesToShareFromIntent = MutableLiveData<ArrayList<String>>()

    val messageToForwardEvent: MutableLiveData<Event<MessageModel>> by lazy {
        MutableLiveData<Event<MessageModel>>()
    }

    var displayedChatRoom: ChatRoom? = null // Prevents the need to go look for the chat room

    val showConversationEvent: MutableLiveData<Event<String>> by lazy {
        MutableLiveData<Event<String>>()
    }

    val hideConversationEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    // When using keyboard to share gif or other, see RichContentReceiver & RichEditText classes
    val richContentUri = MutableLiveData<Event<Uri>>()

    val displayFileEvent: MutableLiveData<Event<Bundle>> by lazy {
        MutableLiveData<Event<Bundle>>()
    }

    val forceRefreshDisplayedConversationEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val forceRefreshConversationInfoEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val forceRefreshConversationEvents: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val newChatMessageEphemeralLifetimeToSetEvent: MutableLiveData<Event<Long>> by lazy {
        MutableLiveData<Event<Long>>()
    }

    val updateConversationLastMessageEvent: MutableLiveData<Event<String>> by lazy {
        MutableLiveData<Event<String>>()
    }

    val updateUnreadMessageCountForCurrentConversationEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    // Meetings related

    var displayedMeeting: ConferenceInfo? = null // Prevents the need to go look for the conference info

    val meetingEditedEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val forceRefreshMeetingsListEvent: MutableLiveData<Event<Boolean>> by lazy {
        MutableLiveData<Event<Boolean>>()
    }

    val goToMeetingWaitingRoomEvent: MutableLiveData<Event<String>> by lazy {
        MutableLiveData<Event<String>>()
    }

    val goToScheduleMeetingEvent: MutableLiveData<Event<Pair<String, ArrayList<String>>>> by lazy {
        MutableLiveData<Event<Pair<String, ArrayList<String>>>>()
    }

    // Recordings related

    var playingRecording: RecordingModel? = null

    // Other

    val mediaViewerFullScreenMode = MutableLiveData<Boolean>()

    val listOfSelectedSipUrisEvent: MutableLiveData<Event<ArrayList<String>>> by lazy {
        MutableLiveData<Event<ArrayList<String>>>()
    }
}
