
package org.linphone.ui.main.model

import androidx.annotation.UiThread
import androidx.annotation.WorkerThread
import androidx.lifecycle.MutableLiveData
import org.linphone.core.Address
import org.linphone.core.Friend
import org.linphone.ui.main.contacts.model.ContactAvatarModel
import org.linphone.utils.AppUtils
import org.linphone.utils.LinphoneUtils

class ConversationContactOrSuggestionModel
    @WorkerThread
    constructor(
    val address: Address,
    val conversationId: String = "",
    conversationSubject: String? = null,
    val friend: Friend? = null,
    private val onClicked: ((Address) -> Unit)? = null
) {
    val id = friend?.refKey ?: address.asStringUriOnly().hashCode()

    val starred = friend?.starred == true

    val name = conversationSubject
        ?: if (friend != null) {
            friend.name ?: LinphoneUtils.getDisplayName(address)
        } else {
            address.username.orEmpty()
        }

    val sipUri = address.asStringUriOnly()

    val initials = AppUtils.getInitials(conversationSubject ?: name)

    val avatarModel = MutableLiveData<ContactAvatarModel>()

    val selected = MutableLiveData<Boolean>()

    @UiThread
    fun onClicked() {
        onClicked?.invoke(address)
    }
}
