<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.ui.NotoSansFont" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.viewmodel.AbstractMainViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_nav_bar"
        android:layout_width="@dimen/landscape_nav_bar_width"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/bottom_nav_bar_label_style"
            android:id="@+id/contacts"
            android:onClick="@{() -> viewModel.navigateToContacts()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/address_book"
            android:drawablePadding="10dp"
            android:drawableTint="@{viewModel.contactsSelected ? @color/main1_500 : @color/main2_600, default=@color/main2_600}"
            android:text="@string/bottom_navigation_contacts_label"
            textFont="@{viewModel.contactsSelected ? NotoSansFont.NotoSansBold : NotoSansFont.NotoSansRegular}"
            app:layout_constraintBottom_toTopOf="@id/calls"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/bottom_nav_bar_label_style"
            android:id="@+id/calls"
            android:onClick="@{() -> viewModel.navigateToHistory()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/phone"
            android:drawableTint="@{viewModel.callsSelected ? @color/main1_500 : @color/main2_600, default=@color/main2_600}"
            android:drawablePadding="10dp"
            android:text="@string/bottom_navigation_calls_label"
            textFont="@{viewModel.callsSelected ? NotoSansFont.NotoSansBold : NotoSansFont.NotoSansRegular}"
            app:layout_constraintBottom_toTopOf="@id/conversations"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/contacts" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/missed_calls"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:text="@{String.valueOf(viewModel.missedCallsCount), default=`1`}"
            android:visibility="@{viewModel.missedCallsCount > 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toTopOf="@id/calls"
            app:layout_constraintEnd_toEndOf="@id/calls"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/bottom_nav_bar_label_style"
            android:id="@+id/conversations"
            android:onClick="@{() -> viewModel.navigateToConversations()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/chat_teardrop_text"
            android:drawablePadding="10dp"
            android:drawableTint="@{viewModel.conversationsSelected ? @color/main1_500 : @color/main2_600, default=@color/main2_600}"
            android:text="@string/bottom_navigation_conversations_label"
            android:visibility="@{viewModel.hideConversations ? View.GONE : View.VISIBLE}"
            textFont="@{viewModel.conversationsSelected ? NotoSansFont.NotoSansBold : NotoSansFont.NotoSansRegular}"
            app:layout_constraintBottom_toTopOf="@id/meetings"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/calls" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/unread_messages"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:text="@{String.valueOf(viewModel.unreadMessages), default=`100`}"
            android:visibility="@{viewModel.unreadMessages > 0 &amp;&amp; !viewModel.hideConversations ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toTopOf="@id/conversations"
            app:layout_constraintEnd_toEndOf="@id/conversations"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/bottom_nav_bar_label_style"
            android:id="@+id/meetings"
            android:onClick="@{() -> viewModel.navigateToMeetings()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:drawableTop="@drawable/video_conference"
            android:drawablePadding="10dp"
            android:drawableTint="@{viewModel.meetingsSelected ? @color/main1_500 : @color/main2_600, default=@color/main2_600}"
            android:text="@string/bottom_navigation_meetings_label"
            android:visibility="@{viewModel.hideMeetings ? View.GONE : View.VISIBLE}"
            textFont="@{viewModel.meetingsSelected ? NotoSansFont.NotoSansBold : NotoSansFont.NotoSansRegular}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/conversations" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>