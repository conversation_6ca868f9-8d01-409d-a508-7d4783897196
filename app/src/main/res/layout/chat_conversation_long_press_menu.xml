<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <data>
        <import type="android.view.View" />
        <variable
            name="markAsReadClickListener"
            type="View.OnClickListener" />
        <variable
            name="toggleMuteClickListener"
            type="View.OnClickListener" />
        <variable
            name="callClickListener"
            type="View.OnClickListener" />
        <variable
            name="deleteClickListener"
            type="View.OnClickListener" />
        <variable
            name="leaveClickListener"
            type="View.OnClickListener" />
        <variable
            name="isMuted"
            type="Boolean" />
        <variable
            name="isGroup"
            type="Boolean" />
        <variable
            name="isReadOnly"
            type="Boolean" />
        <variable
            name="hasUnreadMessages"
            type="Boolean" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_separator">

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{markAsReadClickListener}"
            style="@style/context_menu_action_label_style"
            android:id="@+id/mark_as_read"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="1dp"
            android:visibility="@{hasUnreadMessages ? View.VISIBLE : View.GONE}"
            android:text="@string/conversation_action_mark_as_read"
            android:background="@drawable/menu_item_background"
            android:drawableStart="@drawable/envelope_simple_open"
            app:layout_constraintBottom_toTopOf="@id/mute"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{toggleMuteClickListener}"
            style="@style/context_menu_action_label_style"
            android:id="@+id/mute"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{isMuted ? @string/conversation_action_unmute : @string/conversation_action_mute, default=@string/conversation_action_mute}"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@{isMuted ? @drawable/bell_simple : @drawable/bell_simple_slash, default=@drawable/bell_simple_slash}"
            android:visibility="@{isReadOnly ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toTopOf="@id/call"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{callClickListener}"
            style="@style/context_menu_action_label_style"
            android:id="@+id/call"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/conversation_action_call"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/phone"
            android:visibility="@{isGroup || isReadOnly ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toTopOf="@id/delete_chat_room"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{deleteClickListener}"
            style="@style/context_menu_danger_action_label_style"
            android:id="@+id/delete_chat_room"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/conversation_action_delete"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/trash_simple"
            app:layout_constraintBottom_toTopOf="@id/leave_group"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{leaveClickListener}"
            style="@style/context_menu_action_label_style"
            android:visibility="@{isGroup &amp;&amp; !isReadOnly ? View.VISIBLE : View.GONE}"
            android:id="@+id/leave_group"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/conversation_action_leave_group"
            android:background="@drawable/menu_item_background"
            android:layout_marginBottom="1dp"
            android:drawableStart="@drawable/sign_out"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>