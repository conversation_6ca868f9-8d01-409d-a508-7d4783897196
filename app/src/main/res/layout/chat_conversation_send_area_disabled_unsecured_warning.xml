<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="5dp"
        android:paddingBottom="10dp">

        <ImageView
            android:id="@+id/disabled_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:contentDescription="@null"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:background="@drawable/shape_squircle_main2_200_chat_event_border"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/disabled_icon"
            android:layout_width="@dimen/icon_size"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:paddingTop="3dp"
            android:adjustViewBounds="true"
            android:contentDescription="@null"
            android:src="@drawable/lock_simple_open_bold"
            app:tint="@color/orange_warning_600"
            app:layout_constraintTop_toTopOf="@id/disabled_title"
            app:layout_constraintBottom_toBottomOf="@id/disabled_subtitle"
            app:layout_constraintStart_toStartOf="@id/disabled_background"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_700"
            android:id="@+id/disabled_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="15dp"
            android:text="@string/conversation_warning_disabled_because_not_secured_title"
            android:textSize="12sp"
            android:textColor="@color/orange_warning_600"
            app:layout_constraintStart_toEndOf="@id/disabled_icon"
            app:layout_constraintEnd_toEndOf="@id/disabled_background"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/disabled_subtitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="10dp"
            android:layout_marginEnd="15dp"
            android:text="@string/conversation_warning_disabled_because_not_secured_subtitle"
            android:textColor="?attr/color_grey_400"
            android:textSize="12sp"
            app:layout_constraintStart_toEndOf="@id/disabled_icon"
            app:layout_constraintEnd_toEndOf="@id/disabled_background"
            app:layout_constraintTop_toBottomOf="@id/disabled_title"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>