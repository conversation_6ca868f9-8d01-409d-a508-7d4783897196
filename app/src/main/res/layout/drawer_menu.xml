<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="settingsClickedListener"
            type="View.OnClickListener" />
        <variable
            name="recordingsClickListener"
            type="View.OnClickListener" />
        <variable
            name="helpClickedListener"
            type="View.OnClickListener" />
        <variable
            name="quitClickedListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.viewmodel.DrawerMenuViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_main2_000"
        android:clickable="true"
        android:focusable="true">

        <ImageView
            android:id="@+id/brand_icon"
            android:onClick="@{() -> viewModel.closeDrawerMenu()}"
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:layout_marginStart="26dp"
            android:layout_marginTop="15dp"
            android:src="@drawable/linphone_notification"
            android:contentDescription="@string/app_name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:tint="?attr/color_main1_500" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/section_header_style"
            android:id="@+id/brand_name"
            android:onClick="@{() -> viewModel.closeDrawerMenu()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/app_name"
            android:gravity="center_vertical"
            android:drawableEnd="@drawable/x"
            android:drawablePadding="15dp"
            app:drawableTint="?attr/color_main2_600"
            app:layout_constraintTop_toTopOf="@id/brand_icon"
            app:layout_constraintStart_toEndOf="@id/brand_icon"
            app:layout_constraintBottom_toBottomOf="@id/brand_icon"
            app:layout_constraintEnd_toEndOf="parent" />

        <ScrollView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="15dp"
            android:layout_marginBottom="15dp"
            android:fillViewport="true"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/brand_name"
            app:layout_constraintBottom_toTopOf="@id/separator">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                entries="@{viewModel.accounts}"
                layout="@{@layout/account_list_cell}">

                <ImageView
                    android:id="@+id/no_account_image"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:src="@drawable/illu"
                    android:layout_margin="10dp"
                    android:contentDescription="@null"
                    app:layout_constraintHeight_max="200dp"
                    app:layout_constraintBottom_toTopOf="@id/no_contacts_label"
                    app:layout_constraintDimensionRatio="1:1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/background" />

                <androidx.appcompat.widget.AppCompatTextView
                    style="@style/section_header_style"
                    android:id="@+id/no_account_label"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/drawer_menu_no_account_configured_yet"
                    android:gravity="center"
                    app:layout_constraintBottom_toTopOf="@id/background"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/no_contacts_image" />

            </LinearLayout>

        </ScrollView>


        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginBottom="15dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/color_separator"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/shortcuts" />

        <LinearLayout
            android:id="@+id/shortcuts"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="10dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:orientation="vertical"
            android:visibility="@{viewModel.shortcuts.empty ? View.GONE : View.VISIBLE, default=gone}"
            app:entries="@{viewModel.shortcuts}"
            app:layout="@{@layout/drawer_shortcuts_list_cell}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/settings" />

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{settingsClickedListener}"
            style="@style/header_style"
            android:id="@+id/settings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/settings_title"
            android:drawableStart="@drawable/gear"
            android:drawableEnd="@drawable/caret_right"
            android:drawablePadding="8dp"
            android:visibility="@{viewModel.hideSettings ? View.GONE : View.VISIBLE}"
            app:drawableTint="?attr/color_main2_500"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/recordings" />

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{recordingsClickListener}"
            style="@style/header_style"
            android:id="@+id/recordings"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/recordings_title"
            android:drawableStart="@drawable/record_fill"
            android:drawableEnd="@drawable/caret_right"
            android:drawablePadding="8dp"
            android:visibility="@{viewModel.hideRecordings ? View.GONE : View.VISIBLE}"
            app:drawableTint="?attr/color_main2_500"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/help" />

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{helpClickedListener}"
            style="@style/header_style"
            android:id="@+id/help"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/help_title"
            android:drawableStart="@drawable/question"
            android:drawableEnd="@drawable/caret_right"
            android:drawablePadding="8dp"
            app:drawableTint="?attr/color_main2_500"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/separator_2" />

        <View
            android:id="@+id/separator_2"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginBottom="15dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:background="?attr/color_separator"
            android:visibility="@{viewModel.hideQuitButton ? View.GONE : View.VISIBLE, default=gone}"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toTopOf="@id/quit" />

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{quitClickedListener}"
            style="@style/header_style"
            android:id="@+id/quit"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="15dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/help_quit_title"
            android:visibility="@{viewModel.hideQuitButton ? View.GONE : View.VISIBLE, default=gone}"
            android:drawableStart="@drawable/power"
            android:drawablePadding="8dp"
            app:drawableTint="?attr/color_main2_500"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>