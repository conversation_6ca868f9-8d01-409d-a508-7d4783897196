<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.SendMessageInConversationViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_main2_000">

        <View
            android:id="@+id/participants_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?attr/color_separator"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/participants"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            app:layout_constraintHeight_max="@dimen/chat_room_participants_list_max_height"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/participants_separator">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="5dp"
                android:orientation="vertical"
                entries="@{viewModel.participants}"
                layout="@{@layout/chat_participant_list_cell}"/>

        </androidx.core.widget.NestedScrollView>

        <ImageView
            android:id="@+id/participants_close"
            android:onClick="@{() -> viewModel.closeParticipantsList()}"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/x"
            android:contentDescription="@string/content_description_chat_close_participants_list"
            app:tint="@color/icon_color_selector"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/participants_separator" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>