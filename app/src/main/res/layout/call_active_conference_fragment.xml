<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="shareConferenceClickListener"
            type="View.OnClickListener" />
        <variable
            name="callsListClickListener"
            type="View.OnClickListener" />
        <variable
            name="participantsListClickListener"
            type="View.OnClickListener" />
        <variable
            name="callStatisticsClickListener"
            type="View.OnClickListener" />
        <variable
            name="callMediaEncryptionStatisticsClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
        <variable
            name="conferenceViewModel"
            type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
        <variable
            name="callsViewModel"
            type="org.linphone.ui.call.viewmodel.CallsViewModel" />
        <variable
            name="numpadModel"
            type="org.linphone.ui.main.history.model.NumpadModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/constraint_layout"
            android:onClick="@{() -> viewModel.conferenceModel.toggleFullScreen()}"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/gray_900">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/hinge_top"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/hinge_bottom"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="1" />

            <androidx.constraintlayout.widget.Group
                android:id="@+id/header_info_visibility"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="@{viewModel.fullScreenMode || viewModel.pipMode ? View.INVISIBLE : View.VISIBLE}"
                app:constraint_referenced_ids="back, conference_subject, separator, chronometer" />

            <ImageView
                android:id="@+id/back"
                android:onClick="@{backClickListener}"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/top_bar_height"
                android:adjustViewBounds="true"
                android:padding="15dp"
                android:src="@drawable/caret_left"
                android:contentDescription="@string/content_description_go_back_icon"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/hinge_top"
                app:tint="@color/bc_white" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/call_header_style"
                android:id="@+id/conference_subject"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:text="@{conferenceViewModel.subject, default=`Meeting with John Doe`}"
                android:maxLines="1"
                android:ellipsize="end"
                app:layout_constrainedWidth="true"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintEnd_toStartOf="@id/separator"
                app:layout_constraintTop_toTopOf="@id/back"
                app:layout_constraintBottom_toBottomOf="@id/back"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/call_header_style"
                android:id="@+id/separator"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/vertical_separator"
                app:layout_constraintStart_toEndOf="@id/conference_subject"
                app:layout_constraintEnd_toStartOf="@id/chronometer"
                app:layout_constraintTop_toTopOf="@id/conference_subject"/>

            <Chronometer
                style="@style/call_header_style"
                android:id="@+id/chronometer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                app:layout_constraintStart_toEndOf="@id/separator"
                app:layout_constraintEnd_toStartOf="@id/paused_call_header"
                app:layout_constraintTop_toTopOf="@id/conference_subject"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/call_header_style"
                android:id="@+id/paused_call_header"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:text="@string/call_state_paused"
                android:visibility="@{conferenceViewModel.isPaused ? View.VISIBLE : View.GONE, default=gone}"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintStart_toEndOf="@id/chronometer"
                app:layout_constraintEnd_toStartOf="@id/switch_camera"
                app:layout_constraintTop_toTopOf="@id/conference_subject"/>

            <ImageView
                android:id="@+id/switch_camera"
                android:onClick="@{() -> viewModel.switchCamera()}"
                android:layout_width="@dimen/big_icon_size"
                android:layout_height="0dp"
                android:padding="10dp"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:src="@drawable/camera_rotate"
                android:contentDescription="@string/content_description_change_camera"
                android:visibility="@{!viewModel.fullScreenMode &amp;&amp; !viewModel.pipMode &amp;&amp; viewModel.showSwitchCamera &amp;&amp; conferenceViewModel.isMeParticipantSendingVideo ? View.VISIBLE : View.GONE}"
                app:tint="@color/bc_white"
                app:layout_constraintTop_toTopOf="@id/back"
                app:layout_constraintBottom_toBottomOf="@id/back"
                app:layout_constraintEnd_toStartOf="@+id/paused" />

            <ImageView
                android:id="@+id/paused"
                android:layout_width="@dimen/big_icon_size"
                android:layout_height="0dp"
                android:padding="10dp"
                android:src="@drawable/pause"
                android:contentDescription="@string/content_description_paused_call"
                android:visibility="@{!viewModel.fullScreenMode &amp;&amp; !viewModel.pipMode &amp;&amp; conferenceViewModel.isPaused ? View.VISIBLE : View.GONE, default=gone}"
                app:tint="?attr/color_main1_500"
                app:layout_constraintTop_toTopOf="@id/back"
                app:layout_constraintBottom_toBottomOf="@id/back"
                app:layout_constraintEnd_toStartOf="@id/stats" />

            <ImageView
                android:id="@+id/stats"
                android:onClick="@{callStatisticsClickListener}"
                android:layout_width="@dimen/big_icon_size"
                android:layout_height="0dp"
                android:padding="10dp"
                android:src="@{viewModel.qualityIcon, default=@drawable/cell_signal_full}"
                android:contentDescription="@string/content_description_show_call_statistics"
                android:visibility="@{!viewModel.fullScreenMode &amp;&amp; !viewModel.pipMode &amp;&amp; !conferenceViewModel.isPaused ? View.VISIBLE : View.GONE}"
                android:tint="@{viewModel.qualityValue >= 2 ? @color/bc_white : viewModel.qualityValue >= 1 ? @color/orange_warning_600 : @color/red_danger_500, default=@color/bc_white}"
                app:layout_constraintTop_toTopOf="@id/back"
                app:layout_constraintBottom_toBottomOf="@id/back"
                app:layout_constraintEnd_toEndOf="parent"
                bind:ignore="UseAppTint" />

            <include
                android:id="@+id/call_media_encryption_info"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                layout="@layout/call_media_encryption_info"
                bind:viewModel="@{viewModel}"
                bind:callMediaEncryptionStatisticsClickListener="@{callMediaEncryptionStatisticsClickListener}"
                app:layout_constraintTop_toBottomOf="@id/conference_subject"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintEnd_toEndOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_300"
                android:id="@+id/waiting_for_others"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="5dp"
                android:layout_marginEnd="5dp"
                android:text="@string/conference_call_empty"
                android:textColor="@color/bc_white"
                android:textSize="22sp"
                android:gravity="center"
                android:visibility="@{conferenceViewModel.participantDevices.size() > 1 ? View.GONE : View.VISIBLE}"
                app:layout_constraintVertical_chainStyle="packed"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toTopOf="@id/share_conference_link"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_600"
                android:id="@+id/share_conference_link"
                android:onClick="@{shareConferenceClickListener}"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="20dp"
                android:paddingTop="12dp"
                android:paddingBottom="12dp"
                android:paddingStart="20dp"
                android:paddingEnd="20dp"
                android:background="@drawable/shape_squircle_in_call_button_background"
                android:text="@string/conference_share_link_title"
                android:textSize="18sp"
                android:textColor="@color/gray_main2_400"
                android:visibility="@{conferenceViewModel.participantDevices.size() > 1 || viewModel.pipMode ? View.GONE : View.VISIBLE}"
                android:drawableStart="@drawable/share_network"
                android:drawablePadding="8dp"
                app:drawableTint="@color/gray_main2_400"
                app:layout_constraintTop_toBottomOf="@id/waiting_for_others"
                app:layout_constraintBottom_toBottomOf="@id/hinge_bottom"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

            <org.linphone.ui.call.view.RoundCornersTextureView
                android:id="@+id/local_preview_video_surface"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="10dp"
                android:layout_marginBottom="@{viewModel.fullScreenMode || viewModel.pipMode || viewModel.halfOpenedFolded ? @dimen/zero : @dimen/call_main_actions_menu_margin, default=@dimen/call_main_actions_menu_margin}"
                android:visibility="@{conferenceViewModel.isMeParticipantSendingVideo &amp;&amp; conferenceViewModel.participants.size() == 1 &amp;&amp; !conferenceViewModel.isPaused ? View.VISIBLE : View.GONE}"
                app:alignTopRight="true"
                app:displayMode="black_bars"
                roundCornersRadius="@{viewModel.pipMode ? @dimen/call_pip_round_corners_texture_view_radius : @dimen/call_round_corners_texture_view_radius, default=@dimen/call_round_corners_texture_view_radius}"
                app:layout_constraintBottom_toBottomOf="@id/hinge_bottom"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_max="@{viewModel.pipMode ? @dimen/call_video_preview_pip_max_size : @dimen/call_video_preview_max_size, default=@dimen/call_video_preview_max_size}"
                app:layout_constraintWidth_max="@{viewModel.pipMode ? @dimen/call_video_preview_pip_max_size : @dimen/call_video_preview_max_size, default=@dimen/call_video_preview_max_size}" />

            <androidx.fragment.app.FragmentContainerView
                android:id="@+id/conference_layout_nav_host_fragment"
                android:name="androidx.navigation.fragment.NavHostFragment"
                android:onClick="@{() -> viewModel.conferenceModel.toggleFullScreen()}"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginBottom="@{viewModel.fullScreenMode || viewModel.pipMode || viewModel.halfOpenedFolded ? @dimen/zero : @dimen/call_main_actions_menu_margin, default=@dimen/call_main_actions_menu_margin}"
                android:layout_marginTop="@{viewModel.fullScreenMode || viewModel.pipMode || viewModel.halfOpenedFolded ? @dimen/zero : @dimen/call_remote_video_top_margin, default=@dimen/call_remote_video_top_margin}"
                android:visibility="@{conferenceViewModel.participants.size() > 1 &amp;&amp; !conferenceViewModel.isPaused ? View.VISIBLE : View.GONE, default=gone}"
                app:navGraph="@navigation/conference_nav_graph"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintBottom_toBottomOf="@id/hinge_bottom"/>

            <ImageView
                android:id="@+id/recording"
                android:layout_width="@dimen/icon_size"
                android:layout_height="@dimen/icon_size"
                android:layout_marginTop="10dp"
                android:layout_marginStart="10dp"
                android:src="@drawable/record_fill"
                android:contentDescription="@string/content_description_call_is_being_recorded"
                android:visibility="@{viewModel.isRecording ? View.VISIBLE : View.GONE, default=gone}"
                app:layout_constraintTop_toBottomOf="@id/call_media_encryption_info"
                app:layout_constraintStart_toStartOf="parent"
                app:tint="?attr/color_danger_500" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            android:id="@+id/bottom_bar"
            layout="@layout/call_conference_actions_bottom_sheet"
            bind:viewModel="@{viewModel}"
            bind:callsViewModel="@{callsViewModel}"
            bind:participantsListClickListener="@{participantsListClickListener}"
            bind:callsListClickListener="@{callsListClickListener}"/>

        <include
            android:id="@+id/call_media_encryption_stats"
            layout="@layout/call_media_encryption_stats_bottom_sheet"
            bind:viewModel="@{viewModel}"
            bind:model="@{viewModel.callMediaEncryptionModel}"/>

        <include
            android:id="@+id/call_stats"
            layout="@layout/call_stats_bottom_sheet"
            bind:viewModel="@{viewModel}"
            bind:model="@{viewModel.callStatsModel}"/>

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>