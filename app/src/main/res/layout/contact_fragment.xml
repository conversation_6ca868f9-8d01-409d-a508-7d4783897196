<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.core.ConsolidatedPresence" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="shareClickListener"
            type="View.OnClickListener" />
        <variable
            name="deleteClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.contacts.viewmodel.ContactViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/color_background_contrast_in_dark_mode">

            <ImageView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:adjustViewBounds="true"
                android:padding="15dp"
                android:onClick="@{backClickListener}"
                android:visibility="@{viewModel.showBackButton ? View.VISIBLE : View.GONE}"
                android:src="@drawable/caret_left"
                android:contentDescription="@string/content_description_go_back_icon"
                app:tint="?attr/color_main1_500"
                app:layout_constraintBottom_toBottomOf="@id/invisible_title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/invisible_title"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/main_page_title_style"
                android:id="@+id/invisible_title"
                android:visibility="invisible"
                android:layout_width="0dp"
                android:layout_height="@dimen/top_bar_height"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                app:layout_constraintEnd_toStartOf="@id/edit"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="parent"/>

            <ImageView
                android:onClick="@{() -> viewModel.editContact()}"
                android:id="@+id/edit"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:padding="15dp"
                android:adjustViewBounds="true"
                android:src="@drawable/pencil_simple"
                android:contentDescription="@string/content_description_contact_edit"
                android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                app:tint="?attr/color_main2_500"
                app:layout_constraintBottom_toBottomOf="@id/invisible_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="@id/invisible_title" />

            <ScrollView
                android:id="@+id/scrollView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="?attr/color_grey_100"
                android:fillViewport="true"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/invisible_title"
                app:layout_constraintBottom_toBottomOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:paddingBottom="@dimen/screen_bottom_margin">

                    <androidx.constraintlayout.widget.Group
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="company_label, company"
                        android:visibility="@{viewModel.company.length() > 0 ? View.VISIBLE : View.GONE}" />

                    <androidx.constraintlayout.widget.Group
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="title_label, title"
                        android:visibility="@{viewModel.title.length() > 0 ? View.VISIBLE : View.GONE}" />

                    <androidx.constraintlayout.widget.Group
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:constraint_referenced_ids="trusted_devices_count, trusted_devices_progress, devices, trusted_devices_progress_label, trusted_devices_progress_background"
                        android:visibility="@{viewModel.showContactTrustAndDevices ? (viewModel.expandDevicesTrust &amp;&amp; viewModel.devices.size() > 0 ? View.VISIBLE : View.GONE) : View.GONE, default=gone}" />

                    <include
                        android:id="@+id/avatar"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        layout="@layout/contact_avatar_big"
                        bind:model="@{viewModel.contact}"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/name"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="10dp"
                        android:text="@{viewModel.contact.name, default=`John Doe`}"
                        android:textColor="?attr/color_main2_800"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/avatar" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_300"
                        android:id="@+id/status"
                        android:visibility="@{viewModel.contact.presenceStatus == ConsolidatedPresence.Offline ? View.GONE : View.VISIBLE}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="@{viewModel.contact.lastPresenceInfo, default=@string/contact_presence_status_online}"
                        android:textColor="@{viewModel.contact.presenceStatus == ConsolidatedPresence.Online ? @color/success_500 : @color/warning_600, default=@color/success_500}"
                        android:textSize="14sp"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/name" />

                    <ImageView
                        android:id="@+id/call"
                        android:onClick="@{() -> viewModel.startAudioCall()}"
                        android:enabled="@{viewModel.atLeastOneSipAddressOrPhoneNumber}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/phone"
                        android:contentDescription="@string/content_description_call_start"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintEnd_toStartOf="@id/chat"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/call_label"
                        android:onClick="@{() -> viewModel.startAudioCall()}"
                        android:enabled="@{viewModel.atLeastOneSipAddressOrPhoneNumber}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/contact_call_action"
                        android:textSize="14sp"
                        app:layout_constraintTop_toBottomOf="@id/call"
                        app:layout_constraintStart_toStartOf="@id/call"
                        app:layout_constraintEnd_toEndOf="@id/call"/>

                    <ImageView
                        android:id="@+id/chat"
                        android:onClick="@{() -> viewModel.goToConversation()}"
                        android:visibility="@{viewModel.chatDisabled ? View.GONE : View.VISIBLE}"
                        android:enabled="@{viewModel.atLeastOneSipAddressOrPhoneNumber}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/chat_teardrop_text"
                        android:contentDescription="@string/content_description_chat_create"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintEnd_toStartOf="@id/video_call"
                        app:layout_constraintStart_toEndOf="@id/call"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/chat_label"
                        android:onClick="@{() -> viewModel.goToConversation()}"
                        android:visibility="@{viewModel.chatDisabled ? View.GONE : View.VISIBLE}"
                        android:enabled="@{viewModel.atLeastOneSipAddressOrPhoneNumber}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/contact_message_action"
                        android:textSize="14sp"
                        app:layout_constraintTop_toBottomOf="@id/chat"
                        app:layout_constraintStart_toStartOf="@id/chat"
                        app:layout_constraintEnd_toEndOf="@id/chat"/>

                    <ImageView
                        android:id="@+id/video_call"
                        android:onClick="@{() -> viewModel.startVideoCall()}"
                        android:visibility="@{viewModel.videoCallDisabled ? View.GONE : View.VISIBLE}"
                        android:enabled="@{viewModel.atLeastOneSipAddressOrPhoneNumber}"
                        android:layout_width="56dp"
                        android:layout_height="56dp"
                        android:layout_marginTop="20dp"
                        android:background="@drawable/circle_light_blue_button_background"
                        android:padding="16dp"
                        android:src="@drawable/video_camera"
                        android:contentDescription="@string/content_description_call_start_video"
                        app:tint="?attr/color_main2_500"
                        app:layout_constraintStart_toEndOf="@id/chat"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/status" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/video_call_label"
                        android:onClick="@{() -> viewModel.startVideoCall()}"
                        android:visibility="@{viewModel.videoCallDisabled ? View.GONE : View.VISIBLE}"
                        android:enabled="@{viewModel.atLeastOneSipAddressOrPhoneNumber}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginTop="8dp"
                        android:text="@string/contact_video_call_action"
                        android:textSize="14sp"
                        app:layout_constraintTop_toBottomOf="@id/video_call"
                        app:layout_constraintStart_toStartOf="@id/video_call"
                        app:layout_constraintEnd_toEndOf="@id/video_call"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/section_header_style"
                        android:id="@+id/numbers_and_addresses_label"
                        android:onClick="@{() -> viewModel.toggleNumbersAndAddressesExpand()}"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:padding="5dp"
                        android:layout_marginStart="26dp"
                        android:layout_marginEnd="26dp"
                        android:layout_marginTop="32dp"
                        android:text="@string/contact_details_numbers_and_addresses_title"
                        android:drawableEnd="@{viewModel.expandNumbersAndAddresses ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                        android:drawableTint="?attr/color_main2_600"
                        android:visibility="@{viewModel.atLeastOneSipAddressOrPhoneNumber ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/call_label"/>

                    <LinearLayout
                        android:id="@+id/numbers_and_addresses"
                        android:visibility="@{viewModel.atLeastOneSipAddressOrPhoneNumber &amp;&amp; viewModel.expandNumbersAndAddresses ? View.VISIBLE : View.GONE}"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginTop="8dp"
                        android:padding="10dp"
                        android:background="@drawable/shape_squircle_white_background"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/numbers_and_addresses_label"
                        app:entries="@{viewModel.sipAddressesAndPhoneNumbers}"
                        app:layout="@{@layout/contact_number_address_list_cell}" />

                    <ImageView
                        android:id="@+id/info_background"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="16dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/shape_squircle_white_background"
                        android:contentDescription="@null"
                        android:visibility="@{viewModel.title.length() > 0 || viewModel.company.length() > 0 ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/numbers_and_addresses"
                        app:layout_constraintBottom_toBottomOf="@id/title_label"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/header_style"
                        android:id="@+id/company_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:text="@string/contact_details_company_name"
                        app:layout_constraintStart_toStartOf="@id/info_background"
                        app:layout_constraintTop_toTopOf="@id/info_background"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/company"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:text="@{viewModel.company, default=`Belledonne Comm`}"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:layout_constraintStart_toEndOf="@id/company_label"
                        app:layout_constraintEnd_toEndOf="@id/info_background"
                        app:layout_constraintTop_toTopOf="@id/info_background"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/header_style"
                        android:id="@+id/title_label"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="10dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:text="@string/contact_details_job_title"
                        app:layout_constraintStart_toStartOf="@id/info_background"
                        app:layout_constraintTop_toBottomOf="@id/company"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style"
                        android:id="@+id/title"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="5dp"
                        android:paddingTop="8dp"
                        android:paddingBottom="8dp"
                        android:text="@{viewModel.title, default=`Android developer`}"
                        android:textSize="14sp"
                        android:maxLines="1"
                        android:ellipsize="end"
                        app:layout_constraintEnd_toEndOf="@id/info_background"
                        app:layout_constraintStart_toEndOf="@id/title_label"
                        app:layout_constraintTop_toBottomOf="@id/company"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/section_header_style"
                        android:id="@+id/trust_label"
                        android:onClick="@{() -> viewModel.displayTrustDialog()}"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="26dp"
                        android:layout_marginTop="16dp"
                        android:padding="5dp"
                        android:text="@string/contact_details_trust_title"
                        android:drawableEnd="@drawable/question"
                        android:drawableTint="?attr/color_main2_600"
                        android:drawablePadding="8dp"
                        android:visibility="@{viewModel.showContactTrustAndDevices ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/info_background"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/section_header_style"
                        android:id="@+id/trust_toggle"
                        android:onClick="@{() -> viewModel.toggleDevicesTrustExpand()}"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginEnd="26dp"
                        android:layout_marginTop="16dp"
                        android:padding="5dp"
                        android:drawableEnd="@{viewModel.expandDevicesTrust ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                        android:drawableTint="?attr/color_main2_600"
                        android:visibility="@{viewModel.showContactTrustAndDevices ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintStart_toEndOf="@id/trust_label"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/info_background"/>

                    <androidx.constraintlayout.widget.Barrier
                        android:id="@+id/trust_background_bottom_barrier"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        app:barrierDirection="bottom"
                        app:constraint_referenced_ids="devices, no_device_label" />

                    <ImageView
                        android:id="@+id/trust_background"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:layout_marginTop="8dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:src="@drawable/shape_squircle_white_background"
                        android:contentDescription="@null"
                        android:visibility="@{viewModel.expandDevicesTrust ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/trust_label"
                        app:layout_constraintBottom_toBottomOf="@id/trust_background_bottom_barrier"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/header_style"
                        android:id="@+id/trusted_devices_count"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginTop="20dp"
                        android:text="@string/contact_details_trusted_devices_count"
                        app:layout_constraintStart_toStartOf="@id/trust_background"
                        app:layout_constraintEnd_toEndOf="@id/trust_background"
                        app:layout_constraintTop_toTopOf="@id/trust_background" />

                    <View
                        android:id="@+id/trusted_devices_progress_background"
                        android:layout_width="0dp"
                        android:layout_height="28dp"
                        android:layout_marginTop="10dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/shape_squircle_contact_trust_r50_background"
                        app:layout_constraintStart_toStartOf="@id/trust_background"
                        app:layout_constraintEnd_toEndOf="@id/trust_background"
                        app:layout_constraintTop_toBottomOf="@id/trusted_devices_count" />

                    <com.google.android.material.progressindicator.LinearProgressIndicator
                        android:id="@+id/trusted_devices_progress"
                        android:layout_width="0dp"
                        android:layout_height="24dp"
                        android:layout_marginStart="3dp"
                        android:layout_marginEnd="3dp"
                        android:max="100"
                        android:progress="@{viewModel.trustedDevicesPercentage, default=0}"
                        app:trackCornerRadius="50dp"
                        app:trackThickness="22dp"
                        app:trackColor="@color/transparent_color"
                        app:indicatorColor="?attr/color_trust_track"
                        app:trackStopIndicatorSize="0dp"
                        app:layout_constraintStart_toStartOf="@id/trusted_devices_progress_background"
                        app:layout_constraintEnd_toEndOf="@id/trusted_devices_progress_background"
                        app:layout_constraintTop_toTopOf="@id/trusted_devices_progress_background"
                        app:layout_constraintBottom_toBottomOf="@id/trusted_devices_progress_background"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/trusted_devices_progress_label"
                        android:layout_width="wrap_content"
                        android:layout_height="0dp"
                        android:layout_marginStart="5dp"
                        android:layout_marginEnd="5dp"
                        android:text="@{String.valueOf(viewModel.trustedDevicesPercentage) + `%`, default=`0%`}"
                        android:textColor="@{viewModel.trustedDevicesPercentage > 15 ? @color/bc_white : @color/red_danger_500_night, default=@color/red_danger_500_night}"
                        android:textSize="12sp"
                        app:layout_constraintHorizontal_bias="@{viewModel.trustedDevicesPercentageFloat, default=0.5}"
                        app:layout_constraintStart_toStartOf="@id/trusted_devices_progress"
                        app:layout_constraintEnd_toEndOf="@id/trusted_devices_progress"
                        app:layout_constraintTop_toTopOf="@id/trusted_devices_progress"
                        app:layout_constraintBottom_toBottomOf="@id/trusted_devices_progress" />

                    <LinearLayout
                        android:id="@+id/devices"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:padding="10dp"
                        app:entries="@{viewModel.devices}"
                        app:layout="@{@layout/contact_device_trust_list_cell}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/trusted_devices_progress" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/default_text_style_700"
                        android:id="@+id/no_device_label"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:padding="10dp"
                        android:text="@string/contact_details_no_device_found"
                        android:visibility="@{viewModel.expandDevicesTrust &amp;&amp; viewModel.devices.empty ? View.VISIBLE : View.GONE, default=gone}"
                        app:layout_constraintStart_toStartOf="@id/trust_background"
                        app:layout_constraintEnd_toEndOf="@id/trust_background"
                        app:layout_constraintTop_toTopOf="@id/trust_background"
                        app:layout_constraintBottom_toBottomOf="@id/trust_background" />

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/section_header_style"
                        android:id="@+id/actions"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:padding="5dp"
                        android:layout_marginStart="26dp"
                        android:layout_marginEnd="26dp"
                        android:layout_marginTop="16dp"
                        android:text="@string/contact_details_actions_title"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/trust_background"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:onClick="@{() -> viewModel.editContact()}"
                        style="@style/context_menu_action_label_style"
                        android:id="@+id/action_edit"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:layout_marginTop="8dp"
                        android:text="@string/contact_details_edit"
                        android:drawableStart="@drawable/pencil_simple"
                        android:background="@drawable/action_background_top"
                        android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/actions"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:onClick="@{() -> viewModel.toggleFavourite()}"
                        style="@style/context_menu_action_label_style"
                        android:id="@+id/action_favorite"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/action_background_middle"
                        android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                        android:text="@{viewModel.isFavourite ? @string/contact_details_remove_from_favourites : @string/contact_details_add_to_favourites, default=@string/contact_details_add_to_favourites}"
                        android:drawableStart="@{viewModel.isFavourite ? @drawable/heart_fill : @drawable/heart, default=@drawable/heart_fill}"
                        android:drawableTint="@{viewModel.isFavourite ? @color/danger_500 : @color/main2_500, default=@color/main2_500}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/action_edit" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:onClick="@{shareClickListener}"
                        style="@style/context_menu_action_label_style"
                        android:id="@+id/action_share"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@{viewModel.isStored ? @drawable/action_background_middle : @drawable/action_background_full, default=@drawable/action_background_middle}"
                        android:text="@string/contact_details_share"
                        android:drawableStart="@drawable/share_network"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/action_favorite"/>

                    <androidx.appcompat.widget.AppCompatTextView
                        android:onClick="@{deleteClickListener}"
                        style="@style/context_menu_danger_action_label_style"
                        android:id="@+id/action_delete"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="@drawable/action_background_bottom"
                        android:text="@string/contact_details_delete"
                        android:drawableStart="@drawable/trash_simple"
                        android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                        app:layout_constraintStart_toStartOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toBottomOf="@id/action_share"/>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="?attr/color_separator"
                        android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                        app:layout_constraintEnd_toEndOf="@id/action_edit"
                        app:layout_constraintStart_toStartOf="@id/action_edit"
                        app:layout_constraintTop_toBottomOf="@+id/action_edit"/>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="?attr/color_separator"
                        android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                        app:layout_constraintEnd_toEndOf="@id/action_favorite"
                        app:layout_constraintStart_toStartOf="@id/action_favorite"
                        app:layout_constraintTop_toBottomOf="@+id/action_favorite"/>

                    <View
                        android:layout_width="0dp"
                        android:layout_height="1dp"
                        android:layout_marginStart="16dp"
                        android:layout_marginEnd="16dp"
                        android:background="?attr/color_separator"
                        android:visibility="@{viewModel.isStored ? View.VISIBLE : View.GONE}"
                        app:layout_constraintEnd_toEndOf="@id/action_share"
                        app:layout_constraintStart_toStartOf="@id/action_share"
                        app:layout_constraintTop_toBottomOf="@+id/action_share"/>

                </androidx.constraintlayout.widget.ConstraintLayout>

            </ScrollView>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            layout="@layout/operation_in_progress"
            bind:visibility="@{viewModel.operationInProgress}" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>