<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="model"
            type="org.linphone.ui.main.chat.model.ParticipantModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{() -> model.onClicked()}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp">

        <include
            android:id="@+id/avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            layout="@layout/contact_avatar"
            bind:model="@{model.avatarModel}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{model.avatarModel.name, default=`John Doe`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_800"
            android:layout_marginStart="10dp"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toStartOf="@id/participant_menu"
            app:layout_constraintBottom_toTopOf="@id/sip_address"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/sip_address"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@{model.sipUri, default=`sip:<EMAIL>`}"
            android:visibility="@{model.showSipUri ? View.VISIBLE : View.GONE, default=gone}"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_500"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintTop_toBottomOf="@id/name"
            app:layout_constraintEnd_toEndOf="@id/name"
            app:layout_constraintBottom_toTopOf="@id/admin_label"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/admin_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/conversation_info_participant_is_admin_label"
            android:textColor="?attr/color_main2_500"
            android:textSize="12sp"
            android:visibility="@{model.isParticipantAdmin ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/sip_address"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintEnd_toEndOf="@id/name"
            app:layout_constraintBottom_toBottomOf="parent" />

        <ImageView
            android:id="@+id/participant_menu"
            android:onClick="@{() -> model.openMenu(participantMenu)}"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:src="@drawable/dots_three_vertical"
            android:contentDescription="@string/content_description_show_popup_menu"
            android:visibility="@{model.showMenu ? View.VISIBLE : View.GONE}"
            app:tint="?attr/color_grey_800"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/name"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginEnd="10dp"
            android:background="?attr/color_separator"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>