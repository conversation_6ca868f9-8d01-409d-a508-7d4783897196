<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.core.ConsolidatedPresence" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="editSubjectClickListener"
            type="View.OnClickListener" />
        <variable
            name="addParticipantsClickListener"
            type="View.OnClickListener" />
        <variable
            name="goToContactClickListener"
            type="View.OnClickListener" />
        <variable
            name="addToContactsClickListener"
            type="View.OnClickListener" />
        <variable
            name="configureEphemeralMessagesClickListener"
            type="View.OnClickListener" />
        <variable
            name="deleteHistoryClickListener"
            type="View.OnClickListener" />
        <variable
            name="copySipUriClickListener"
            type="View.OnClickListener" />
        <variable
            name="copyPeerSipUriClickListener"
            type="View.OnClickListener" />
        <variable
            name="goToSharedMediaClickListener"
            type="View.OnClickListener" />
        <variable
            name="goToSharedDocumentsClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.ConversationInfoViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?attr/color_background_contrast_in_dark_mode">

        <ImageView
            android:id="@+id/back"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:padding="15dp"
            android:onClick="@{backClickListener}"
            android:src="@drawable/caret_left"
            android:contentDescription="@string/content_description_go_back_icon"
            app:tint="?attr/color_main1_500"
            app:layout_constraintBottom_toBottomOf="@id/invisible_title"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/invisible_title"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/main_page_title_style"
            android:id="@+id/invisible_title"
            android:visibility="invisible"
            android:layout_width="0dp"
            android:layout_height="@dimen/top_bar_height"
            app:layout_constraintEnd_toStartOf="@id/show_menu"
            app:layout_constraintStart_toEndOf="@id/back"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/show_menu"
            android:onLongClick="@{() -> viewModel.showDebugInfo()}"
            android:layout_width="@dimen/top_bar_height"
            android:layout_height="@dimen/top_bar_height"
            android:contentDescription="@string/content_description_show_popup_menu"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="parent"/>

        <androidx.core.widget.NestedScrollView
            android:id="@+id/scrollView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="?attr/color_grey_100"
            android:fillViewport="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/invisible_title">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="@dimen/screen_bottom_margin">

                <include
                    android:id="@+id/avatar"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    layout="@layout/contact_avatar_big"
                    bind:model="@{viewModel.avatarModel}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/title"
                    style="@style/default_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{viewModel.isGroup ? viewModel.subject : viewModel.avatarModel.name, default=`John Doe`}"
                    android:textColor="?attr/color_main2_800"
                    android:textSize="14sp"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/avatar" />

                <ImageView
                    android:id="@+id/edit_subject"
                    android:layout_width="@dimen/icon_size"
                    android:layout_height="@dimen/icon_size"
                    android:layout_marginStart="8dp"
                    android:onClick="@{editSubjectClickListener}"
                    android:src="@drawable/pencil_simple"
                    android:contentDescription="@string/content_description_chat_edit_conversation_subject"
                    android:visibility="@{!viewModel.isGroup || viewModel.isReadOnly || !viewModel.isMyselfAdmin ? View.GONE : View.VISIBLE}"
                    app:tint="?attr/color_main2_600"
                    app:layout_constraintBottom_toBottomOf="@id/title"
                    app:layout_constraintStart_toEndOf="@id/title"
                    app:layout_constraintTop_toTopOf="@id/title" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/address"
                    style="@style/default_text_style"
                    android:onClick="@{copySipUriClickListener}"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:ellipsize="end"
                    android:maxLines="1"
                    android:text="@{viewModel.sipUri, default=`sip:<EMAIL>`}"
                    android:textColor="?attr/color_main2_800"
                    android:textSize="14sp"
                    android:drawableEnd="@drawable/copy"
                    android:drawablePadding="5dp"
                    android:visibility="@{viewModel.isGroup ? View.GONE : View.VISIBLE, default=gone}"
                    app:drawableTint="?attr/color_main2_600"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/title" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/peer_address"
                    style="@style/default_text_style"
                    android:onClick="@{copyPeerSipUriClickListener}"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:ellipsize="end"
                    android:maxLines="2"
                    android:text="@{viewModel.peerSipUri, default=`sip:<EMAIL>`}"
                    android:textColor="?attr/color_main2_800"
                    android:textSize="14sp"
                    android:drawableEnd="@drawable/copy"
                    android:drawablePadding="5dp"
                    android:visibility="@{viewModel.showPeerSipUri ? View.VISIBLE : View.GONE, default=gone}"
                    app:drawableTint="?attr/color_main2_600"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/address" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/status"
                    style="@style/default_text_style_300"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@{viewModel.avatarModel.lastPresenceInfo, default=@string/contact_presence_status_online}"
                    android:textColor="@{viewModel.avatarModel.presenceStatus == ConsolidatedPresence.Online ? @color/success_500 : @color/warning_600, default=@color/success_500}"
                    android:textSize="14sp"
                    android:visibility="@{viewModel.isGroup || viewModel.avatarModel.presenceStatus == ConsolidatedPresence.Offline ? View.GONE : View.VISIBLE, default=gone}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/peer_address" />

                <ImageView
                    android:id="@+id/mute"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/circle_light_blue_button_background"
                    android:onClick="@{() -> viewModel.toggleMute()}"
                    android:padding="16dp"
                    android:contentDescription="@string/content_description_chat_toggle_mute"
                    android:src="@{viewModel.isMuted ? @drawable/bell_simple : @drawable/bell_simple_slash, default=@drawable/bell_simple_slash}"
                    android:visibility="@{viewModel.isReadOnly ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toStartOf="@id/call"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/status"
                    app:tint="?attr/color_main2_500" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/mute_label"
                    style="@style/default_text_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="5dp"
                    android:onClick="@{() -> viewModel.toggleMute()}"
                    android:text="@{viewModel.isMuted ? @string/conversation_action_unmute : @string/conversation_action_mute, default=@string/conversation_action_mute}"
                    android:textSize="14sp"
                    android:textAlignment="center"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:visibility="@{viewModel.isReadOnly ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toStartOf="@id/call_label"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/mute" />

                <ImageView
                    android:id="@+id/call"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/circle_light_blue_button_background"
                    android:onClick="@{() -> viewModel.startCall()}"
                    android:padding="16dp"
                    android:src="@drawable/phone"
                    android:contentDescription="@string/content_description_call_start"
                    android:visibility="@{viewModel.isReadOnly ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toStartOf="@id/meeting"
                    app:layout_constraintStart_toEndOf="@id/mute"
                    app:layout_constraintTop_toBottomOf="@id/status"
                    app:tint="?attr/color_main2_500" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/call_label"
                    style="@style/default_text_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:onClick="@{() -> viewModel.startCall()}"
                    android:text="@string/conversation_action_call"
                    android:textSize="14sp"
                    android:textAlignment="center"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:visibility="@{viewModel.isReadOnly ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toEndOf="@id/call"
                    app:layout_constraintStart_toStartOf="@id/call"
                    app:layout_constraintTop_toBottomOf="@id/call" />

                <ImageView
                    android:id="@+id/meeting"
                    android:layout_width="56dp"
                    android:layout_height="56dp"
                    android:layout_marginTop="40dp"
                    android:background="@drawable/circle_light_blue_button_background"
                    android:onClick="@{() -> viewModel.scheduleMeeting()}"
                    android:padding="16dp"
                    android:src="@drawable/video_conference"
                    android:contentDescription="@string/content_description_schedule_meeting"
                    android:visibility="@{viewModel.isReadOnly ? View.GONE : View.VISIBLE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/call"
                    app:layout_constraintTop_toBottomOf="@id/status"
                    app:tint="?attr/color_main2_500" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/meeting_label"
                    style="@style/default_text_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:layout_marginStart="5dp"
                    android:onClick="@{() -> viewModel.scheduleMeeting()}"
                    android:text="@string/meeting_schedule_meeting_label"
                    android:textSize="14sp"
                    android:textAlignment="center"
                    android:maxLines="2"
                    android:ellipsize="end"
                    android:visibility="@{viewModel.isReadOnly ? View.GONE : View.VISIBLE}"
                    app:layout_constraintStart_toEndOf="@id/call_label"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/meeting" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/participants_label"
                    style="@style/section_header_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:layout_marginTop="32dp"
                    android:layout_marginEnd="26dp"
                    android:drawableEnd="@{viewModel.expandParticipants ? @drawable/caret_up : @drawable/caret_down, default=@drawable/caret_up}"
                    android:drawableTint="?attr/color_main2_600"
                    android:onClick="@{() -> viewModel.toggleParticipantsExpand()}"
                    android:padding="5dp"
                    android:text="@{viewModel.participantsLabel, default=@string/conversation_info_participants_list_title}"
                    android:visibility="@{viewModel.isGroup ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/mute_label" />

                <View
                    android:id="@+id/participants_background"
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="-16dp"
                    android:background="@drawable/shape_squircle_white_background"
                    android:visibility="@{!viewModel.expandParticipants || !viewModel.isMyselfAdmin || !viewModel.isGroup || viewModel.isReadOnly ? View.GONE : View.VISIBLE, default=gone}"
                    app:layout_constraintBottom_toBottomOf="@id/add_participants"
                    app:layout_constraintTop_toTopOf="@id/participants" />

                <androidx.recyclerview.widget.RecyclerView
                    android:id="@+id/participants"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginTop="8dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="16dp"
                    android:background="@drawable/shape_squircle_white_background"
                    android:nestedScrollingEnabled="true"
                    android:visibility="@{viewModel.expandParticipants &amp;&amp; viewModel.isGroup ? View.VISIBLE : View.GONE}"
                    app:layout_constraintBottom_toTopOf="@id/add_participants"
                    app:layout_constrainedHeight="true"
                    app:layout_constraintTop_toBottomOf="@id/participants_label"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/add_participants"
                    style="@style/tertiary_button_label_style"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="10dp"
                    android:background="@drawable/tertiary_button_background"
                    android:drawableStart="@drawable/plus_circle"
                    android:drawablePadding="8dp"
                    android:ellipsize="end"
                    android:gravity="center"
                    android:maxLines="1"
                    android:onClick="@{addParticipantsClickListener}"
                    android:paddingStart="16dp"
                    android:paddingTop="10dp"
                    android:paddingEnd="16dp"
                    android:paddingBottom="10dp"
                    android:text="@string/conversation_info_add_participants_label"
                    android:visibility="@{!viewModel.expandParticipants || !viewModel.isMyselfAdmin || !viewModel.isGroup || viewModel.isReadOnly ? View.GONE : View.VISIBLE, default=gone}"
                    app:drawableTint="?attr/color_main1_500"
                    app:layout_constraintBottom_toTopOf="@id/media_documents_actions"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/participants" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/media_documents_actions"
                    style="@style/section_header_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="26dp"
                    android:padding="5dp"
                    android:text="@string/conversation_details_media_documents_title"
                    app:layout_constraintBottom_toTopOf="@id/action_media"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/add_participants" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_media"
                    style="@style/context_menu_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/action_background_top"
                    android:drawableStart="@drawable/image"
                    android:onClick="@{goToSharedMediaClickListener}"
                    android:text="@string/conversation_media_list_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/media_documents_actions" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_documents"
                    style="@style/context_menu_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginBottom="@dimen/screen_bottom_margin"
                    android:background="@drawable/action_background_bottom"
                    android:drawableStart="@drawable/file_pdf"
                    android:onClick="@{goToSharedDocumentsClickListener}"
                    android:text="@string/conversation_document_list_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_media" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/color_separator"
                    app:layout_constraintEnd_toEndOf="@id/action_media"
                    app:layout_constraintStart_toStartOf="@id/action_media"
                    app:layout_constraintTop_toBottomOf="@id/action_media" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/other_actions"
                    style="@style/section_header_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="26dp"
                    android:layout_marginTop="16dp"
                    android:layout_marginEnd="26dp"
                    android:padding="5dp"
                    android:text="@string/contact_details_actions_title"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_documents" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_see_contact"
                    style="@style/context_menu_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:layout_marginTop="8dp"
                    android:background="@drawable/action_background_top"
                    android:drawableStart="@drawable/address_book"
                    android:onClick="@{goToContactClickListener}"
                    android:text="@string/conversation_info_menu_go_to_contact"
                    android:visibility="@{!viewModel.isGroup &amp;&amp; viewModel.friendAvailable ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/other_actions" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_add_to_contacts"
                    style="@style/context_menu_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@drawable/action_background_top"
                    android:drawableStart="@drawable/user_plus"
                    android:onClick="@{addToContactsClickListener}"
                    android:text="@string/conversation_info_menu_add_to_contacts"
                    android:visibility="@{!viewModel.isGroup &amp;&amp; !viewModel.friendAvailable ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_see_contact" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_ephemeral_messages"
                    style="@style/context_menu_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@{!viewModel.isGroup ? @drawable/action_background_middle : @drawable/action_background_top, default=@drawable/action_background_top}"
                    android:drawableStart="@drawable/clock_countdown"
                    android:onClick="@{configureEphemeralMessagesClickListener}"
                    android:text="@string/conversation_action_configure_ephemeral_messages"
                    android:visibility="@{viewModel.isEndToEndEncrypted &amp;&amp; !viewModel.isReadOnly ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_add_to_contacts" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_leave_group"
                    style="@style/context_menu_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@{viewModel.isEndToEndEncrypted &amp;&amp; !viewModel.isReadOnly ? @drawable/action_background_middle : @drawable/action_background_top, default=@drawable/action_background_middle}"
                    android:drawableStart="@drawable/sign_out"
                    android:onClick="@{() -> viewModel.leaveGroup()}"
                    android:text="@string/conversation_action_leave_group"
                    android:visibility="@{viewModel.isGroup &amp;&amp; !viewModel.isReadOnly ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_ephemeral_messages" />

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/action_delete"
                    style="@style/context_menu_danger_action_label_style"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="@{viewModel.isGroup &amp;&amp; viewModel.isReadOnly ? @drawable/action_background_full : @drawable/action_background_bottom, default=@drawable/action_background_bottom}"
                    android:drawableStart="@drawable/trash_simple"
                    android:onClick="@{deleteHistoryClickListener}"
                    android:text="@string/conversation_info_delete_history_action"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toBottomOf="@id/action_leave_group" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/color_separator"
                    android:visibility="@{!viewModel.isGroup &amp;&amp; !viewModel.friendAvailable ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintEnd_toEndOf="@id/action_add_to_contacts"
                    app:layout_constraintStart_toStartOf="@id/action_add_to_contacts"
                    app:layout_constraintTop_toBottomOf="@+id/action_add_to_contacts" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/color_separator"
                    android:visibility="@{!viewModel.isGroup &amp;&amp; viewModel.friendAvailable ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintEnd_toEndOf="@id/action_see_contact"
                    app:layout_constraintStart_toStartOf="@id/action_see_contact"
                    app:layout_constraintTop_toBottomOf="@+id/action_see_contact" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/color_separator"
                    android:visibility="@{viewModel.isEndToEndEncrypted &amp;&amp; !viewModel.isReadOnly ? View.VISIBLE : View.GONE}"
                    app:layout_constraintEnd_toEndOf="@id/action_ephemeral_messages"
                    app:layout_constraintStart_toStartOf="@id/action_ephemeral_messages"
                    app:layout_constraintTop_toBottomOf="@+id/action_ephemeral_messages" />

                <View
                    android:layout_width="0dp"
                    android:layout_height="1dp"
                    android:layout_marginStart="16dp"
                    android:layout_marginEnd="16dp"
                    android:background="?attr/color_separator"
                    android:visibility="@{viewModel.isGroup &amp;&amp; !viewModel.isReadOnly ? View.VISIBLE : View.GONE, default=gone}"
                    app:layout_constraintEnd_toEndOf="@id/action_leave_group"
                    app:layout_constraintStart_toStartOf="@id/action_leave_group"
                    app:layout_constraintTop_toBottomOf="@+id/action_leave_group" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>