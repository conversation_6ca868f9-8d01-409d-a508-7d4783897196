<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/call_main_actions_menu_height"
        android:background="@drawable/shape_call_bottom_sheet_background">

        <ImageView
            android:onClick="@{() -> viewModel.toggleExpandActionsMenu()}"
            android:id="@+id/call_actions_handle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="11dp"
            android:src="@drawable/animated_caret_to_handle"
            android:contentDescription="@string/content_description_toggle_bottom_sheet"
            app:tint="@color/bc_white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:onClick="@{() -> viewModel.hangUp()}"
            android:id="@+id/hang_up"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginStart="16dp"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:paddingTop="15dp"
            android:paddingBottom="15dp"
            android:src="@drawable/phone_disconnect"
            android:contentDescription="@string/content_description_hang_up_call"
            android:background="@drawable/squircle_red_button_background"
            app:tint="@color/bc_white"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <ImageView
            android:onClick="@{() -> viewModel.toggleVideo()}"
            android:id="@+id/toggle_video"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:enabled="@{!viewModel.isPaused &amp;&amp; !viewModel.isPausedByRemote &amp;&amp; !viewModel.videoUpdateInProgress}"
            android:visibility="@{viewModel.hideVideo ? View.GONE : View.VISIBLE}"
            android:src="@{viewModel.isSendingVideo ? @drawable/video_camera : @drawable/video_camera_slash, default=@drawable/video_camera}"
            android:background="@drawable/in_call_button_background_red"
            android:contentDescription="@string/content_description_toggle_video"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/hang_up"
            app:layout_constraintEnd_toStartOf="@id/toggle_mute_mic" />

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/video_update_in_progress"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="@{viewModel.videoUpdateInProgress ? View.VISIBLE : View.GONE}"
            android:indeterminate="true"
            app:indicatorColor="?attr/color_main1_500"
            app:layout_constraintTop_toTopOf="@id/toggle_video"
            app:layout_constraintBottom_toBottomOf="@id/toggle_video"
            app:layout_constraintStart_toStartOf="@id/toggle_video"
            app:layout_constraintEnd_toEndOf="@id/toggle_video"/>

        <ImageView
            android:onClick="@{() -> viewModel.toggleMuteMicrophone()}"
            android:id="@+id/toggle_mute_mic"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:selected="@{viewModel.isMicrophoneMuted}"
            android:src="@{viewModel.isMicrophoneMuted ? @drawable/microphone_slash : @drawable/microphone, default=@drawable/microphone}"
            android:background="@drawable/in_call_button_background_red"
            android:contentDescription="@string/content_description_toggle_microphone"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/toggle_video"
            app:layout_constraintEnd_toStartOf="@id/change_audio_output" />

        <ImageView
            android:onClick="@{() -> viewModel.changeAudioOutputDevice()}"
            android:id="@+id/change_audio_output"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@{viewModel.isHearingAidEnabled ? @drawable/ear : viewModel.isHeadsetEnabled ? @drawable/headset : viewModel.isBluetoothEnabled ? @drawable/bluetooth : viewModel.isSpeakerEnabled ? @drawable/speaker_high : @drawable/speaker_slash, default=@drawable/speaker_slash}"
            android:background="@drawable/in_call_button_background_red"
            android:contentDescription="@string/content_description_change_output_audio_device"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/toggle_mute_mic"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>