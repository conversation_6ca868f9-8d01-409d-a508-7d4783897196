<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.ConversationMediaListViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/color_background_contrast_in_dark_mode">

            <ImageView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="0dp"
                android:adjustViewBounds="true"
                android:padding="15dp"
                android:onClick="@{backClickListener}"
                android:src="@drawable/caret_left"
                android:contentDescription="@string/content_description_go_back_icon"
                app:tint="?attr/color_main1_500"
                app:layout_constraintBottom_toBottomOf="@id/title"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="@id/title"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/main_page_title_style"
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="@dimen/top_bar_height"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:text="@string/conversation_media_list_title"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="parent"/>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/mediaList"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="?attr/color_grey_100"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/default_text_style_800"
                android:id="@+id/no_result"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/conversation_no_media_found"
                android:textColor="?attr/color_main2_600"
                android:textSize="16sp"
                android:visibility="@{viewModel.mediaList.empty ? View.VISIBLE : View.GONE}"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"/>

        </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/operation_in_progress"
        bind:visibility="@{viewModel.operationInProgress}" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>