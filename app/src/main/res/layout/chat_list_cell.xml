<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.ui.NotoSansFont" />
        <variable
            name="model"
            type="org.linphone.ui.main.chat.model.ConversationModel" />
        <variable
            name="onClickListener"
            type="View.OnClickListener" />
        <variable
            name="onLongClickListener"
            type="View.OnLongClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{onClickListener}"
        android:onLongClickListener="@{onLongClickListener}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingTop="8dp"
        android:paddingBottom="8dp"
        android:paddingStart="16dp"
        android:paddingEnd="16dp"
        android:background="@drawable/primary_cell_background">

        <include
            android:id="@+id/avatar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="5dp"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            layout="@layout/contact_avatar"
            bind:model="@{model.avatarModel}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/removal_in_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/animated_in_progress"
            android:contentDescription="@string/content_description_chat_removal_in_progress"
            android:visibility="@{model.isBeingDeleted ? View.VISIBLE : View.GONE, default=gone}"
            animatedDrawable="@{true}"
            app:layout_constraintBottom_toBottomOf="@id/avatar"
            app:layout_constraintStart_toStartOf="@id/avatar"
            app:layout_constraintTop_toTopOf="@id/avatar"
            app:layout_constraintEnd_toEndOf="@id/avatar"/>

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/right_border"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:barrierMargin="-5dp"
            app:constraint_referenced_ids="notifications_count, warning_not_secured, ephemeral, muted, date_time, last_sent_message_status" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_500"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="@{model.isGroup ? model.subject : model.avatarModel.name, default=`John Doe`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_800"
            textFont="@{model.unreadMessageCount > 0 ? NotoSansFont.NotoSansExtraBold : NotoSansFont.NotoSansMedium}"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintEnd_toStartOf="@id/right_border"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toTopOf="@id/last_message_or_composing"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/last_message_sender"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="3sp"
            android:gravity="center_vertical|start"
            android:text="@{model.lastMessageTextSender, default=`John Doe:`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            android:visibility="@{model.isBeingDeleted || model.lastMessageTextSender.length() == 0 || model.isComposing ? View.GONE : View.VISIBLE}"
            textFont="@{model.isBeingDeleted || model.unreadMessageCount > 0 || model.isComposing ? NotoSansFont.NotoSansBold : NotoSansFont.NotoSansRegular}"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintEnd_toStartOf="@id/last_message_forward"
            app:layout_constraintTop_toBottomOf="@id/title"
            app:layout_constraintBottom_toTopOf="@id/separator"/>

        <ImageView
            android:id="@+id/last_message_forward"
            android:layout_width="@dimen/small_icon_size"
            android:layout_height="@dimen/small_icon_size"
            android:layout_marginStart="@{model.lastMessageTextSender.length() > 0 ? @dimen/five : @dimen/zero}"
            android:layout_marginEnd="5dp"
            android:layout_marginTop="3dp"
            android:src="@{model.lastMessageContentIcon, default=@drawable/forward}"
            android:visibility="@{model.lastMessageContentIcon > 0 &amp;&amp; !model.isComposing ? View.VISIBLE : View.GONE}"
            android:contentDescription="@null"
            app:tint="?attr/color_main2_600"
            app:layout_constraintStart_toEndOf="@id/last_message_sender"
            app:layout_constraintEnd_toStartOf="@id/last_message_or_composing"
            app:layout_constraintTop_toTopOf="@id/last_message_or_composing"
            app:layout_constraintBottom_toBottomOf="@id/last_message_or_composing"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/last_message_or_composing"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:gravity="center_vertical|start"
            android:maxLines="1"
            android:ellipsize="end"
            android:text="@{model.isBeingDeleted ? @string/conversations_list_is_being_removed_label : model.isComposing ? model.composingLabel : model.lastMessageText, default=`Hello there!`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_500"
            textFont="@{model.isBeingDeleted || model.unreadMessageCount > 0 || model.isComposing ? NotoSansFont.NotoSansBold : NotoSansFont.NotoSansRegular}"
            app:layout_constraintStart_toEndOf="@id/last_message_forward"
            app:layout_constraintEnd_toStartOf="@id/right_border"
            app:layout_constraintTop_toTopOf="@id/last_message_sender"
            app:layout_constraintBottom_toBottomOf="@id/last_message_sender" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/date_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:text="@{model.dateTime, default=`16:45`}"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_500"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="@id/title"
            app:layout_constraintBottom_toBottomOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/notifications_count"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:layout_marginEnd="10dp"
            android:text="@{String.valueOf(model.unreadMessageCount), default=`99`}"
            android:visibility="@{model.unreadMessageCount > 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toBottomOf="@id/date_time"
            app:layout_constraintBottom_toTopOf="@id/separator"/>

        <ImageView
            android:id="@+id/last_sent_message_status"
            android:layout_width="@dimen/small_icon_size"
            android:layout_height="@dimen/small_icon_size"
            android:layout_marginEnd="10dp"
            android:contentDescription="@string/content_description_chat_bubble_delivery_status"
            android:src="@{model.lastMessageDeliveryIcon, default=@drawable/animated_in_progress}"
            android:visibility="@{model.isLastMessageOutgoing ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toStartOf="@id/notifications_count"
            app:layout_constraintTop_toBottomOf="@id/date_time"
            app:layout_constraintBottom_toTopOf="@id/separator"
            app:tint="?attr/color_main1_500" />

        <ImageView
            android:id="@+id/muted"
            android:layout_width="@dimen/small_icon_size"
            android:layout_height="@dimen/small_icon_size"
            android:layout_marginEnd="10dp"
            android:src="@drawable/bell_simple_slash"
            android:contentDescription="@string/content_description_chat_muted"
            android:visibility="@{model.isMuted ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toStartOf="@id/last_sent_message_status"
            app:layout_constraintTop_toBottomOf="@id/date_time"
            app:layout_constraintBottom_toTopOf="@id/separator"
            app:tint="?attr/color_main2_600" />

        <ImageView
            android:id="@+id/ephemeral"
            android:layout_width="@dimen/small_icon_size"
            android:layout_height="@dimen/small_icon_size"
            android:layout_marginEnd="10dp"
            android:src="@drawable/clock_countdown"
            android:contentDescription="@string/content_description_chat_ephemeral_enabled"
            android:visibility="@{model.isEphemeral ? View.VISIBLE : View.GONE}"
            app:layout_constraintEnd_toStartOf="@id/muted"
            app:layout_constraintTop_toBottomOf="@id/date_time"
            app:layout_constraintBottom_toTopOf="@id/separator"
            app:tint="?attr/color_main2_600" />

        <ImageView
            android:id="@+id/warning_not_secured"
            android:layout_width="@dimen/small_icon_size"
            android:layout_height="@dimen/small_icon_size"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="10dp"
            android:src="@drawable/lock_simple_open_bold"
            android:contentDescription="@string/content_description_chat_unsecured"
            android:visibility="@{model.isEncrypted || !model.isEncryptionAvailable ? View.GONE : View.VISIBLE}"
            app:layout_constraintTop_toBottomOf="@id/date_time"
            app:layout_constraintBottom_toTopOf="@id/separator"
            app:layout_constraintEnd_toStartOf="@id/ephemeral"
            app:tint="?attr/color_warning_600" />

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginEnd="10dp"
            android:background="?attr/color_separator"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/title"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>