<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="onLongClickListener"
            type="View.OnLongClickListener" />
        <variable
            name="model"
            type="org.linphone.ui.main.chat.model.MessageModel" />
        <variable
            name="inflatedVisibility"
            type="Integer" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="@dimen/chat_bubble_voice_record_width"
        android:layout_height="wrap_content"
        android:onLongClick="@{onLongClickListener}"
        android:visibility="@{inflatedVisibility == View.VISIBLE ? View.VISIBLE : View.GONE}"
        inflatedLifecycleOwner="@{true}">

        <ProgressBar
            style="?android:attr/progressBarStyleHorizontal"
            android:id="@+id/voice_record_progress"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:progressDrawable="@drawable/voice_recording_gradient_progress"
            android:progress="@{model.voiceRecordPlayerPosition}"
            android:max="@{model.voiceRecordingDuration}"
            tools:progress="60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/play_pause_voice_record"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:onClick="@{() -> model.togglePlayPauseVoiceRecord()}"
            android:padding="8dp"
            android:contentDescription="@string/content_description_chat_bubble_toggle_voice_message_play_pause"
            android:src="@{model.isPlayingVoiceRecord ? @drawable/pause_fill : @drawable/play_fill, default=@drawable/play_fill}"
            android:background="@drawable/circle_white_button_background"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintBottom_toBottomOf="@id/voice_record_duration"
            app:layout_constraintStart_toStartOf="@id/voice_record_progress"
            app:layout_constraintTop_toTopOf="@id/voice_record_duration"
            app:tint="?attr/color_main1_500" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/voice_record_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:text="@{model.formattedVoiceRecordingDuration, default=`00:00`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_600"
            android:background="@drawable/shape_squircle_white_r50_background"
            app:layout_constraintBottom_toBottomOf="@id/voice_record_progress"
            app:layout_constraintEnd_toEndOf="@id/voice_record_progress"
            app:layout_constraintTop_toTopOf="@id/voice_record_progress"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>