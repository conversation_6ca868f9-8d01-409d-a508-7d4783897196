<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.model.GroupSetOrEditSubjectDialogModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{() -> viewModel.dismiss()}"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.75" />

        <ImageView
            android:id="@+id/dialog_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="12dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="2dp"
            android:src="@drawable/shape_dialog_background"
            android:contentDescription="@null"
            app:layout_constraintWidth_max="@dimen/dialog_max_width"
            app:layout_constraintBottom_toBottomOf="@id/anchor"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/title" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/section_header_style"
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="15dp"
            android:paddingTop="@dimen/dialog_top_bottom_margin"
            android:text="@{viewModel.isGroupConversation ? viewModel.isEdit ? @string/conversation_dialog_edit_subject : @string/conversation_dialog_set_subject : @string/history_group_call_start_dialog_set_subject, default=@string/history_group_call_start_dialog_set_subject}"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintBottom_toTopOf="@id/subject"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatEditText
            style="@style/default_text_style"
            android:id="@+id/subject"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginTop="5dp"
            android:paddingStart="20dp"
            android:paddingEnd="20dp"
            android:hint="@{viewModel.isGroupConversation ? @string/conversation_dialog_subject_hint : @string/history_group_call_start_dialog_subject_hint, default=@string/history_group_call_start_dialog_subject_hint}"
            android:text="@={viewModel.subject, default=`Lorem Ipsum`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_600"
            android:maxLines="1"
            android:background="@{viewModel.emptySubject ? @drawable/shape_edit_text_error_background : @drawable/edit_text_background, default=@drawable/edit_text_background}"
            android:inputType="text|textCapSentences"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintWidth_max="@dimen/text_input_max_width"
            app:layout_constraintBottom_toTopOf="@id/cancel"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/title"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_600"
            android:id="@+id/subject_error"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/conversation_dialog_subject_cant_be_empty_error"
            android:textSize="13sp"
            android:textColor="?attr/color_danger_500"
            android:visibility="@{viewModel.emptySubject ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toBottomOf="@id/subject"
            app:layout_constraintStart_toStartOf="@id/subject"
            app:layout_constraintEnd_toEndOf="@id/subject"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{() -> viewModel.dismiss()}"
            style="@style/secondary_button_label_style"
            android:id="@+id/cancel"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="32dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:text="@string/dialog_cancel"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/subject"
            app:layout_constraintBottom_toTopOf="@id/confirm"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:onClick="@{() -> viewModel.confirm()}"
            style="@style/primary_button_label_style"
            android:id="@+id/confirm"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginStart="15dp"
            android:layout_marginEnd="15dp"
            android:enabled="@{!viewModel.emptySubject}"
            android:text="@string/dialog_confirm"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintTop_toBottomOf="@id/cancel"
            app:layout_constraintBottom_toTopOf="@id/anchor"/>

        <View
            android:id="@+id/anchor"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/dialog_top_bottom_margin"
            app:layout_constraintTop_toBottomOf="@id/confirm"
            app:layout_constraintStart_toStartOf="@id/dialog_background"
            app:layout_constraintEnd_toEndOf="@id/dialog_background"
            app:layout_constraintBottom_toBottomOf="@id/guideline"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>