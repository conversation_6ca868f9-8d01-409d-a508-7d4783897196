<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="handleClickedListener"
            type="View.OnClickListener" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/shape_bottom_sheet_white_background"
        android:elevation="16dp"
        app:behavior_hideable="true"
        app:behavior_peekHeight="0dp"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <com.google.android.material.bottomsheet.BottomSheetDragHandleView
            android:id="@+id/handle"
            android:onClick="@{handleClickedListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="11dp"
            android:src="@drawable/shape_drawer_handle"
            app:tint="?attr/color_bottom_sheet_handle"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <com.google.android.material.tabs.TabLayout
            android:id="@+id/tabs"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@color/transparent_color"
            android:layout_marginBottom="16dp"
            app:layout_constraintTop_toBottomOf="@id/handle"
            app:layout_constraintBottom_toTopOf="@id/bottom_sheet_list"
            app:tabMode="fixed"
            app:tabUnboundedRipple="true"
            app:tabRippleColor="?attr/color_main1_100"
            app:tabGravity="fill"
            app:tabPadding="0dp"
            app:tabInlineLabel="true"
            app:tabIndicatorColor="?attr/color_main1_500"
            app:tabTextColor="?attr/color_main2_400"
            app:tabSelectedTextColor="?attr/color_main2_600" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/bottom_sheet_list"
            android:layout_width="match_parent"
            android:layout_height="250dp"
            android:nestedScrollingEnabled="true"
            app:layout_constraintHeight_min="50dp"
            app:layout_constraintBottom_toBottomOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>