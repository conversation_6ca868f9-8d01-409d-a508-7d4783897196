<?xml version="1.0" encoding="utf-8"?>
<layout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="onClickListener"
            type="View.OnClickListener" />
        <variable
            name="model"
            type="org.linphone.ui.main.model.ConversationContactOrSuggestionModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:onClick="@{onClickListener}"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        android:layout_marginEnd="16dp"
        android:paddingTop="5dp"
        android:paddingBottom="5dp"
        android:background="@drawable/primary_cell_background">

        <ImageView
            style="@style/avatar_imageview"
            android:id="@+id/avatar"
            android:layout_width="@dimen/avatar_list_cell_size"
            android:layout_height="@dimen/avatar_list_cell_size"
            android:layout_marginTop="5dp"
            android:layout_marginBottom="5dp"
            android:contentDescription="@null"
            coilInitials="@{model.initials, default=`JD`}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@{model.sipUri, default=`<EMAIL>`}"
            android:textSize="14sp"
            android:maxLines="1"
            android:ellipsize="end"
            android:textColor="?attr/color_main2_800"
            app:layout_constraintStart_toEndOf="@id/avatar"
            app:layout_constraintEnd_toStartOf="@id/selected"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <ImageView
            android:id="@+id/selected"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:contentDescription="@null"
            android:src="@drawable/check"
            android:visibility="@{model.selected ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="?attr/color_main1_500" />

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:layout_marginEnd="10dp"
            android:background="?attr/color_separator"
            android:visibility="gone"
            app:layout_constraintStart_toStartOf="@id/name"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>