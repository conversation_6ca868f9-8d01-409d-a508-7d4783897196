<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="backClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.ConversationForwardMessageViewModel" />
    </data>

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="?attr/color_background_contrast_in_dark_mode">

            <ImageView
                android:id="@+id/back"
                android:layout_width="wrap_content"
                android:layout_height="@dimen/top_bar_height"
                android:padding="15dp"
                android:adjustViewBounds="true"
                android:onClick="@{backClickListener}"
                android:src="@drawable/caret_left"
                android:contentDescription="@string/content_description_go_back_icon"
                app:tint="?attr/color_main1_500"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/main_page_title_style"
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="@dimen/top_bar_height"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="10dp"
                android:text="@string/conversation_forward_message_title"
                android:textColor="?attr/color_main1_500"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toEndOf="@id/back"
                app:layout_constraintTop_toTopOf="parent"/>

            <View
                android:id="@+id/background"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="?attr/color_main2_000"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/back" />

            <androidx.appcompat.widget.AppCompatEditText
                style="@style/default_text_style"
                android:id="@+id/search_bar"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="20dp"
                android:layout_marginTop="16dp"
                android:layout_marginEnd="20dp"
                android:background="@drawable/edit_text_background"
                android:drawableStart="@drawable/magnifying_glass"
                android:drawablePadding="10dp"
                android:drawableTint="?attr/color_main2_600"
                android:hint="@string/new_conversation_search_bar_filter_hint"
                android:inputType="textPersonName|textNoSuggestions"
                android:paddingStart="15dp"
                android:paddingTop="10dp"
                android:paddingEnd="15dp"
                android:paddingBottom="10dp"
                android:text="@={viewModel.searchFilter}"
                android:textSize="14sp"
                app:layout_constraintHeight_min="48dp"
                app:layout_constraintWidth_max="@dimen/text_input_max_width"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/title" />

            <ImageView
                android:id="@+id/clear_field"
                android:layout_width="@dimen/icon_size"
                android:layout_height="@dimen/icon_size"
                android:layout_marginEnd="15dp"
                android:onClick="@{() -> viewModel.clearFilter()}"
                android:src="@drawable/x"
                android:contentDescription="@string/content_description_clear_filter"
                android:visibility="@{viewModel.searchFilter.length() > 0 ? View.VISIBLE : View.GONE, default=gone}"
                app:layout_constraintBottom_toBottomOf="@id/search_bar"
                app:layout_constraintEnd_toEndOf="@id/search_bar"
                app:layout_constraintTop_toTopOf="@id/search_bar"
                app:tint="?attr/color_main2_600" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/contacts_list"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginTop="8dp"
                android:visibility="@{viewModel.isEmpty ? View.GONE : View.VISIBLE}"
                app:layout_constraintTop_toBottomOf="@id/search_bar"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintBottom_toBottomOf="parent" />

            <com.google.android.material.progressindicator.CircularProgressIndicator
                android:id="@+id/fetch_in_progress"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:indeterminate="true"
                android:visibility="@{viewModel.searchInProgress ? View.VISIBLE : View.GONE}"
                app:indicatorColor="?attr/color_main1_500"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toBottomOf="@id/search_bar"
                app:layout_constraintBottom_toBottomOf="parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <include
            layout="@layout/operation_in_progress"
            bind:visibility="@{viewModel.operationInProgress}" />

    </androidx.coordinatorlayout.widget.CoordinatorLayout>

</layout>