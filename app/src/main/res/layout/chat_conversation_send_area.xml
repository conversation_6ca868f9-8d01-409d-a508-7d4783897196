<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:bind="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="openMediaPickerClickListener"
            type="View.OnClickListener" />
        <variable
            name="openCameraClickListener"
            type="View.OnClickListener" />
        <variable
            name="openFilePickerClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.SendMessageInConversationViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_conversation_send_area_background">
        <!-- Keep behavior to have it at the bottom -->

        <androidx.constraintlayout.widget.Group
            android:id="@+id/standard_messages"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.isVoiceRecording ? View.INVISIBLE : View.VISIBLE}"
            app:constraint_referenced_ids="attach_file, message_area_background, message_to_send" />

        <include
            android:id="@+id/reply_area"
            layout="@layout/chat_conversation_reply_area"
            bind:viewModel="@{viewModel}"
            android:visibility="@{viewModel.isReplying ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/attachments"
            layout="@layout/chat_conversation_attachments_area"
            bind:viewModel="@{viewModel}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.isFileAttachmentsListOpen ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toBottomOf="@id/reply_area" />

        <androidx.emoji2.emojipicker.EmojiPickerView
            android:id="@+id/emoji_picker"
            android:layout_width="match_parent"
            android:layout_height="@dimen/chat_room_emoji_picker_height"
            android:background="?attr/color_grey_100"
            android:visibility="@{viewModel.isEmojiPickerOpen ? View.VISIBLE : View.GONE, default=gone}"
            app:emojiPickedListener="@{(emoji) -> viewModel.insertEmoji(emoji.emoji)}"
            app:layout_constraintTop_toBottomOf="@id/attachments" />

        <include
            android:id="@+id/file_pickers"
            layout="@layout/chat_conversation_file_pickers"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.areFilePickersOpen ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toBottomOf="@id/emoji_picker"
            bind:openMediaPickerClickListener="@{openMediaPickerClickListener}"
            bind:openCameraClickListener="@{openCameraClickListener}"
            bind:openFilePickerClickListener="@{openFilePickerClickListener}" />

        <include
            android:id="@+id/participants"
            layout="@layout/chat_conversation_participants_area"
            bind:viewModel="@{viewModel}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:visibility="@{viewModel.isParticipantsListOpen ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toBottomOf="@id/file_pickers" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/top_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="bottom"
            app:constraint_referenced_ids="file_pickers, emoji_picker, participants, attachments, reply_area" />

        <ImageView
            android:id="@+id/emoji_picker_toggle"
            android:layout_width="40dp"
            android:layout_height="0dp"
            android:layout_marginStart="5dp"
            android:onClick="@{() -> viewModel.toggleEmojiPickerVisibility()}"
            android:padding="8dp"
            android:contentDescription="@string/content_description_chat_open_emoji_picker"
            android:src="@{viewModel.isEmojiPickerOpen ? @drawable/x : @drawable/smiley, default=@drawable/smiley}"
            android:visibility="@{viewModel.isVoiceRecording ? View.INVISIBLE : (viewModel.isKeyboardOpen &amp;&amp; viewModel.isFileTransferServerAvailable) ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/message_area_background"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/message_area_background"
            app:tint="@color/icon_color_selector" />

        <ImageView
            android:id="@+id/attach_file"
            android:layout_width="40dp"
            android:layout_height="0dp"
            android:layout_marginStart="5dp"
            android:onClick="@{() -> viewModel.toggleFilePickersVisibility()}"
            android:enabled="@{!viewModel.maxNumberOfAttachmentsReached}"
            android:padding="8dp"
            android:contentDescription="@string/content_description_chat_open_attach_file"
            android:src="@{viewModel.areFilePickersOpen ? @drawable/x : @drawable/paperclip, default=@drawable/paperclip}"
            android:visibility="@{viewModel.isVoiceRecording ? View.INVISIBLE : !viewModel.isFileTransferServerAvailable ? View.GONE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="@id/message_area_background"
            app:layout_constraintEnd_toStartOf="@id/message_area_background"
            app:layout_constraintStart_toEndOf="@id/emoji_picker_toggle"
            app:layout_constraintTop_toTopOf="@id/message_area_background"
            app:tint="@color/icon_color_selector" />

        <ImageView
            android:id="@+id/message_area_background"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="5dp"
            android:layout_marginEnd="16dp"
            android:src="@drawable/chat_message_sending_edit_text_background"
            android:contentDescription="@null"
            app:layout_constraintBottom_toBottomOf="@id/message_to_send"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@id/attach_file"
            app:layout_constraintTop_toTopOf="@id/message_to_send" />

        <org.linphone.ui.main.chat.view.RichEditText
            style="@style/default_text_style"
            android:id="@+id/message_to_send"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="24dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="5dp"
            android:layout_marginBottom="16dp"
            android:background="@color/transparent_color"
            android:hint="@string/conversation_text_field_hint"
            android:imeOptions="flagNoPersonalizedLearning"
            android:inputType="textShortMessage|textMultiLine|textAutoComplete|textAutoCorrect|textCapSentences"
            android:maxLines="3"
            android:minHeight="48dp"
            android:text="@={viewModel.textToSend}"
            android:textSize="14sp"
            android:enabled="@{!viewModel.isVoiceRecording}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/send_barrier"
            app:layout_constraintStart_toStartOf="@id/message_area_background"
            app:layout_constraintTop_toBottomOf="@id/top_barrier" />

        <androidx.constraintlayout.widget.Barrier
            android:id="@+id/send_barrier"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:barrierDirection="start"
            app:constraint_referenced_ids="record_voice_message, send_message" />

        <ImageView
            android:id="@+id/send_message"
            android:layout_width="40dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            android:enabled="@{viewModel.textToSend.length() > 0 || viewModel.attachments.size() > 0}"
            android:visibility="@{viewModel.isCallConversation || viewModel.textToSend.length() > 0 || viewModel.attachments.size() > 0 ? View.VISIBLE : View.GONE, default=gone}"
            android:onClick="@{() -> viewModel.sendMessage()}"
            android:padding="8dp"
            android:src="@drawable/paper_plane_right"
            android:contentDescription="@string/content_description_chat_send_message"
            app:layout_constraintBottom_toBottomOf="@id/message_area_background"
            app:layout_constraintEnd_toEndOf="@id/message_area_background"
            app:layout_constraintTop_toTopOf="@id/message_area_background"
            app:tint="@color/icon_primary_color_selector" />

        <ImageView
            android:id="@+id/record_voice_message"
            android:layout_width="40dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            android:visibility="@{viewModel.isCallConversation || viewModel.textToSend.length() > 0 || viewModel.attachments.size() > 0 || viewModel.isVoiceRecording || !viewModel.isFileTransferServerAvailable ? View.GONE : View.VISIBLE}"
            android:onClick="@{() -> viewModel.startVoiceMessageRecording()}"
            android:padding="8dp"
            android:src="@drawable/microphone"
            android:contentDescription="@string/content_description_chat_start_voice_message_recording"
            app:layout_constraintBottom_toBottomOf="@id/message_area_background"
            app:layout_constraintEnd_toEndOf="@id/message_area_background"
            app:layout_constraintTop_toTopOf="@id/message_area_background"
            app:tint="?attr/color_main2_500" />

        <include
            android:id="@+id/voice_recording_area"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            layout="@layout/chat_conversation_record_voice_message_area"
            bind:viewModel="@{viewModel}"
            android:visibility="@{viewModel.isVoiceRecording ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintTop_toTopOf="@id/message_area_background"
            app:layout_constraintBottom_toBottomOf="@id/message_area_background"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>