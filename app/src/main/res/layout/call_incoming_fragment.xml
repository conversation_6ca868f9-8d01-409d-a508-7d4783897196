<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/gray_900">

        <com.google.android.material.progressindicator.CircularProgressIndicator
            android:id="@+id/in_progress"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="5dp"
            android:indeterminate="true"
            app:indicatorColor="@color/bc_white"
            app:indicatorSize="28dp"
            app:layout_constraintStart_toStartOf="@id/avatar"
            app:layout_constraintEnd_toEndOf="@id/avatar"
            app:layout_constraintTop_toBottomOf="@id/call_direction_label"
            app:layout_constraintBottom_toTopOf="@id/avatar" />

        <include
            android:id="@+id/avatar"
            android:layout_width="@dimen/avatar_in_call_size"
            android:layout_height="@dimen/avatar_in_call_size"
            layout="@layout/contact_avatar_huge"
            bind:model="@{viewModel.contact}"
            app:layout_constraintVertical_chainStyle="packed"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/call_direction_label"
            app:layout_constraintBottom_toTopOf="@id/name" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="15dp"
            android:text="@{viewModel.displayedName, default=`John Doe`}"
            android:textColor="@color/bc_white"
            android:textSize="22sp"
            app:layout_constraintTop_toBottomOf="@id/avatar"
            app:layout_constraintBottom_toBottomOf="@id/address"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@{viewModel.displayedAddress, default=`sip:<EMAIL>`}"
            android:textColor="@color/bc_white"
            android:textSize="14sp"
            android:visibility="@{viewModel.conferenceModel.isCurrentCallInConference ? View.GONE : View.VISIBLE}"
            app:layout_constraintTop_toBottomOf="@id/name"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" />

        <org.linphone.ui.call.view.RoundCornersTextureView
            android:id="@+id/remote_video_surface"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <ImageView
            android:id="@+id/call_direction_icon"
            android:layout_width="12dp"
            android:layout_height="12dp"
            android:layout_marginStart="10dp"
            android:adjustViewBounds="true"
            android:src="@drawable/incoming_call"
            android:contentDescription="@null"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="@id/call_direction_label"
            app:layout_constraintBottom_toBottomOf="@id/call_direction_label"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/call_header_style"
            android:id="@+id/call_direction_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginTop="10dp"
            android:layout_marginBottom="10dp"
            android:text="@{viewModel.incomingCallTitle, default=@string/call_video_incoming}"
            app:layout_constraintStart_toEndOf="@id/call_direction_icon"
            app:layout_constraintTop_toTopOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/early_media_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:text="@{viewModel.displayedName, default=`John Doe`}"
            android:textColor="@color/bc_white"
            android:textSize="22sp"
            android:visibility="@{viewModel.isIncomingEarlyMedia &amp;&amp; viewModel.isVideoEnabled ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintBottom_toTopOf="@id/early_media_address"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/early_media_address"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="10dp"
            android:layout_marginBottom="5dp"
            android:text="@{viewModel.displayedAddress, default=`sip:<EMAIL>`}"
            android:textColor="@color/bc_white"
            android:textSize="14sp"
            android:visibility="@{viewModel.isIncomingEarlyMedia &amp;&amp; viewModel.isVideoEnabled ? View.VISIBLE : View.GONE, default=gone}"
            app:layout_constraintBottom_toTopOf="@id/bottom_bar"
            app:layout_constraintStart_toStartOf="parent" />

        <include
            bind:viewModel="@{viewModel}"
            android:id="@+id/bottom_bar"
            layout="@layout/call_incoming_actions"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_main_actions_menu_height"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>