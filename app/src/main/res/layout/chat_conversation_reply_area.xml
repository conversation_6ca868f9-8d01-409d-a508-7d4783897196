<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.SendMessageInConversationViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="?attr/color_main2_000">

        <View
            android:id="@+id/reply_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="?attr/color_separator"
            app:layout_constraintTop_toTopOf="parent"/>

        <ImageView
            android:id="@+id/cancel"
            android:onClick="@{() -> viewModel.cancelReply()}"
            android:layout_width="@dimen/icon_size"
            android:layout_height="@dimen/icon_size"
            android:layout_marginEnd="10dp"
            android:src="@drawable/x"
            android:contentDescription="@string/content_description_chat_cancel_reply"
            app:tint="@color/icon_color_selector"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/reply_header"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginTop="10dp"
            android:text="@string/conversation_reply_to_message_title"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_500"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_700"
            android:id="@+id/reply_to_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            android:layout_marginStart="5dp"
            android:text="@{viewModel.isReplyingTo, default=`John Doe`}"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_500"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@id/reply_header"
            app:layout_constraintEnd_toStartOf="@id/cancel" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style_300"
            android:id="@+id/reply_content"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="20dp"
            android:layout_marginBottom="10dp"
            android:text="@{viewModel.isReplyingToMessage, default=`Hello this is John! How are you?`}"
            android:textSize="12sp"
            android:textColor="?attr/color_main2_400"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintTop_toBottomOf="@id/reply_header"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/cancel" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>