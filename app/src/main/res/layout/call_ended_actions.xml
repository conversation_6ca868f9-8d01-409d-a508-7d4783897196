<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/call_main_actions_menu_height"
        android:background="@drawable/shape_call_bottom_sheet_background">

        <ImageView
            android:onClick="@{() -> viewModel.toggleExpandActionsMenu()}"
            android:id="@+id/call_actions_handle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="11dp"
            android:src="@drawable/animated_caret_to_handle"
            android:visibility="invisible"
            android:contentDescription="@string/content_description_toggle_bottom_sheet"
            app:tint="@color/bc_white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/hang_up"
            android:layout_width="wrap_content"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginStart="16dp"
            android:paddingStart="30dp"
            android:paddingEnd="30dp"
            android:paddingTop="15dp"
            android:paddingBottom="15dp"
            android:src="@drawable/phone_disconnect"
            android:contentDescription="@null"
            android:background="@drawable/shape_squircle_red_disabled_background"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

        <ImageView
            android:id="@+id/toggle_video"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/video_camera_slash"
            android:contentDescription="@null"
            android:enabled="@{false}"
            android:background="@drawable/shape_round_in_call_disabled_button_background"
            android:visibility="@{viewModel.hideVideo ? View.GONE : View.VISIBLE}"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintHorizontal_chainStyle="packed"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/hang_up"
            app:layout_constraintEnd_toStartOf="@id/toggle_mute_mic" />

        <ImageView
            android:id="@+id/toggle_mute_mic"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/microphone_slash"
            android:contentDescription="@null"
            android:enabled="@{false}"
            android:background="@drawable/shape_round_in_call_disabled_button_background"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/toggle_video"
            app:layout_constraintEnd_toStartOf="@id/change_audio_output" />

        <ImageView
            android:id="@+id/change_audio_output"
            android:layout_width="@dimen/call_button_size"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginEnd="16dp"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/speaker_slash"
            android:contentDescription="@null"
            android:enabled="@{false}"
            android:background="@drawable/shape_round_in_call_disabled_button_background"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintTop_toBottomOf="@id/call_actions_handle"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@id/toggle_mute_mic"
            app:layout_constraintEnd_toEndOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>