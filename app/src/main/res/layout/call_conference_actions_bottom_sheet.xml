<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:bind="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <variable
            name="callsListClickListener"
            type="View.OnClickListener" />
        <variable
            name="participantsListClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.viewmodel.CurrentCallViewModel" />
        <variable
            name="callsViewModel"
            type="org.linphone.ui.call.viewmodel.CallsViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="@dimen/call_all_actions_menu_height"
        android:clickable="true"
        android:focusable="true"
        android:background="@drawable/shape_call_bottom_sheet_background"
        android:visibility="@{viewModel.fullScreenMode || viewModel.pipMode ? View.INVISIBLE : View.VISIBLE}"
        app:behavior_hideable="false"
        app:behavior_peekHeight="@dimen/call_main_actions_menu_height"
        app:layout_behavior="com.google.android.material.bottomsheet.BottomSheetBehavior">

        <include
            android:id="@+id/main_actions"
            layout="@layout/call_actions_generic"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_main_actions_menu_height"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            bind:viewModel="@{viewModel}" />

        <ImageView
            android:id="@+id/screen_sharing"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/monitor_arrow_up"
            android:contentDescription="@string/conference_action_screen_sharing"
            android:background="@drawable/shape_round_in_call_disabled_button_background"
            app:tint="@color/gray_400"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="@id/screen_sharing_label"
            app:layout_constraintEnd_toEndOf="@id/screen_sharing_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions" />

        <ImageView
            android:id="@+id/participants"
            android:onClick="@{participantsListClickListener}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:background="@drawable/in_call_button_background_red"
            android:src="@drawable/users"
            android:contentDescription="@string/conference_action_show_participants"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="@id/participants_label"
            app:layout_constraintEnd_toEndOf="@id/participants_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions" />

        <ImageView
            android:id="@+id/calls_list"
            android:onClick="@{callsListClickListener}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:background="@drawable/in_call_button_background_red"
            android:src="@drawable/phone_list"
            android:contentDescription="@string/call_action_go_to_calls_list"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="@id/calls_list_label"
            app:layout_constraintEnd_toEndOf="@id/calls_list_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/calls_count"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:layout_marginStart="40dp"
            android:layout_marginTop="25dp"
            android:text="@{String.valueOf(callsViewModel.callsCount), default=`1`}"
            android:visibility="@{callsViewModel.callsCount > 1 ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/main_actions"
            app:layout_constraintStart_toStartOf="@id/calls_list"
            app:layout_constraintEnd_toEndOf="@id/calls_list"/>

        <ImageView
            android:id="@+id/layout"
            android:onClick="@{() -> viewModel.conferenceModel.showLayoutMenu()}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:background="@drawable/in_call_button_background_red"
            android:src="@drawable/notebook"
            android:contentDescription="@string/call_action_change_layout"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintStart_toStartOf="@id/layout_label"
            app:layout_constraintEnd_toEndOf="@id/layout_label"
            app:layout_constraintTop_toBottomOf="@id/main_actions" />

        <ImageView
            android:id="@+id/chat"
            android:onClick="@{() -> viewModel.conferenceModel.goToConversation()}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:src="@drawable/chat_teardrop_text"
            android:enabled="@{viewModel.conferenceModel.isConversationAvailable}"
            android:contentDescription="@string/call_action_show_messages"
            android:background="@drawable/in_call_button_background_red"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toBottomOf="@id/screen_sharing_label"
            app:layout_constraintStart_toStartOf="@id/screen_sharing"
            app:layout_constraintEnd_toEndOf="@id/screen_sharing"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/unread_count_text_style"
            android:id="@+id/unread_messages_count"
            android:layout_width="@dimen/unread_count_indicator_size"
            android:layout_height="@dimen/unread_count_indicator_size"
            android:layout_marginStart="40dp"
            android:layout_marginTop="25dp"
            android:text="@{String.valueOf(viewModel.unreadMessagesCount), default=`1`}"
            android:visibility="@{viewModel.unreadMessagesCount > 0 ? View.VISIBLE : View.GONE}"
            app:layout_constraintTop_toBottomOf="@id/screen_sharing_label"
            app:layout_constraintStart_toStartOf="@id/chat"
            app:layout_constraintEnd_toEndOf="@id/chat"/>

        <ImageView
            android:id="@+id/pause_call"
            android:onClick="@{() -> viewModel.togglePause()}"
            android:enabled="@{viewModel.canBePaused}"
            android:selected="@{viewModel.isPaused}"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:contentDescription="@{viewModel.isPaused ? @string/call_action_resume_call : @string/call_action_pause_call}"
            android:src="@{viewModel.isPaused ? @drawable/play : @drawable/pause, default=@drawable/pause}"
            android:background="@drawable/in_call_button_background_green"
            app:tint="@color/in_call_button_tint_color"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toBottomOf="@id/participants_label"
            app:layout_constraintStart_toStartOf="@id/participants"
            app:layout_constraintEnd_toEndOf="@id/participants"/>

        <ImageView
            android:id="@+id/record_call"
            android:layout_width="0dp"
            android:layout_height="@dimen/call_button_size"
            android:layout_marginTop="@dimen/call_extra_button_top_margin"
            android:padding="@dimen/call_button_icon_padding"
            android:enabled="false"
            android:src="@drawable/record_fill"
            android:contentDescription="@string/call_action_record_call"
            android:selected="@{viewModel.isRecording()}"
            android:background="@drawable/shape_round_in_call_disabled_button_background"
            app:tint="@color/gray_400"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintTop_toBottomOf="@id/calls_list_label"
            app:layout_constraintStart_toStartOf="@id/calls_list"
            app:layout_constraintEnd_toEndOf="@id/calls_list" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/screen_sharing_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/conference_action_screen_sharing"
            android:enabled="false"
            app:layout_constraintTop_toBottomOf="@id/screen_sharing"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/participants_label"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/participants_label"
            android:onClick="@{participantsListClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:ellipsize="middle"
            android:text="@{viewModel.conferenceModel.participantsLabel, default=@string/conference_action_show_participants}"
            app:layout_constraintTop_toBottomOf="@id/participants"
            app:layout_constraintStart_toEndOf="@id/screen_sharing_label"
            app:layout_constraintEnd_toStartOf="@id/calls_list_label" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/calls_list_label"
            android:onClick="@{callsListClickListener}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/call_action_go_to_calls_list"
            app:layout_constraintTop_toBottomOf="@id/calls_list"
            app:layout_constraintStart_toEndOf="@id/participants_label"
            app:layout_constraintEnd_toStartOf="@id/layout_label" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/layout_label"
            android:onClick="@{() -> viewModel.conferenceModel.showLayoutMenu()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:text="@string/call_action_change_layout"
            app:layout_constraintTop_toBottomOf="@id/layout"
            app:layout_constraintStart_toEndOf="@id/calls_list_label"
            app:layout_constraintEnd_toEndOf="parent" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/chat_label"
            android:onClick="@{() -> viewModel.conferenceModel.goToConversation()}"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_show_messages"
            android:enabled="@{viewModel.conferenceModel.isConversationAvailable}"
            app:layout_constraintTop_toBottomOf="@id/chat"
            app:layout_constraintStart_toStartOf="@id/screen_sharing_label"
            app:layout_constraintEnd_toEndOf="@id/screen_sharing_label" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/pause_call_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:enabled="@{viewModel.canBePaused}"
            android:selected="@{viewModel.isPaused}"
            android:text="@{viewModel.isPaused ? @string/call_action_resume_call : @string/call_action_pause_call, default=@string/call_action_pause_call}"
            app:layout_constraintTop_toBottomOf="@id/pause_call"
            app:layout_constraintStart_toStartOf="@id/participants_label"
            app:layout_constraintEnd_toEndOf="@id/participants_label" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/in_call_extra_action_label_style"
            android:id="@+id/record_call_label"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:paddingBottom="15dp"
            android:text="@string/call_action_record_call"
            android:enabled="false"
            app:layout_constraintTop_toBottomOf="@id/record_call"
            app:layout_constraintStart_toStartOf="@id/calls_list_label"
            app:layout_constraintEnd_toEndOf="@id/calls_list_label" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>