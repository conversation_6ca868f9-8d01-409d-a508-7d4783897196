<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>
        <import type="android.view.View" />
        <import type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
        <variable
            name="gridClickListener"
            type="View.OnClickListener" />
        <variable
            name="activeSpeakerClickListener"
            type="View.OnClickListener" />
        <variable
            name="audioOnlyClickListener"
            type="View.OnClickListener" />
        <variable
            name="viewModel"
            type="org.linphone.ui.call.conference.viewmodel.ConferenceViewModel" />
    </data>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:clickable="true"
        android:focusable="true"
        android:background="@drawable/shape_squircle_gray_600_top_call_background">

        <com.google.android.material.radiobutton.MaterialRadioButton
            style="@style/context_menu_action_label_style"
            android:onClick="@{gridClickListener}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/conference_layout_grid"
            android:textColor="@color/in_call_label_color"
            android:gravity="center_vertical"
            android:layout_marginBottom="1dp"
            android:drawableEnd="@drawable/squares_four"
            android:drawableTint="@color/in_call_label_color"
            android:enabled="@{viewModel.participantDevices.size() &lt; 7 &amp;&amp; !viewModel.isScreenSharing, default=false}"
            android:checked="@{viewModel.conferenceLayout == ConferenceViewModel.GRID_LAYOUT}"
            app:useMaterialThemeColors="false"
            app:buttonTint="@color/in_call_label_color"/>

        <com.google.android.material.radiobutton.MaterialRadioButton
            style="@style/context_menu_action_label_style"
            android:onClick="@{activeSpeakerClickListener}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/conference_layout_active_speaker"
            android:textColor="@color/bc_white"
            android:gravity="center_vertical"
            android:layout_marginBottom="1dp"
            android:drawableEnd="@drawable/picture_in_picture"
            android:drawableTint="@color/bc_white"
            android:checked="@{viewModel.conferenceLayout == ConferenceViewModel.ACTIVE_SPEAKER_LAYOUT}"
            app:useMaterialThemeColors="false"
            app:buttonTint="@color/bc_white"/>

        <com.google.android.material.radiobutton.MaterialRadioButton
            style="@style/context_menu_action_label_style"
            android:onClick="@{audioOnlyClickListener}"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:text="@string/conference_layout_audio_only"
            android:textColor="@color/bc_white"
            android:gravity="center_vertical"
            android:layout_marginBottom="1dp"
            android:drawableEnd="@drawable/waveform"
            android:drawableTint="@color/bc_white"
            android:checked="@{viewModel.conferenceLayout == ConferenceViewModel.AUDIO_ONLY_LAYOUT}"
            app:useMaterialThemeColors="false"
            app:buttonTint="@color/bc_white"/>

    </LinearLayout>

</layout>