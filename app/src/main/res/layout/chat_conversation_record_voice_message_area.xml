<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>
        <import type="android.view.View" />
        <variable
            name="viewModel"
            type="org.linphone.ui.main.chat.viewmodel.SendMessageInConversationViewModel" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/cancel_voice_message"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginStart="8dp"
            android:onClick="@{() -> viewModel.cancelVoiceMessageRecording()}"
            android:padding="8dp"
            android:src="@drawable/x"
            android:contentDescription="@string/content_description_cancel_voice_message_recording"
            android:background="@drawable/circle_white_button_background"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="?attr/color_main1_500" />

        <ProgressBar
            style="?android:attr/progressBarStyleHorizontal"
            android:id="@+id/voice_record_progress"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:progressDrawable="@drawable/voice_recording_gradient_progress"
            android:progress="@{viewModel.voiceRecordPlayerPosition}"
            android:max="@{viewModel.voiceRecordingDuration}"
            tools:progress="60"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/voice_recording_send_message"
            app:layout_constraintStart_toEndOf="@id/cancel_voice_message"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/stop_recording"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:onClick="@{() -> viewModel.stopVoiceMessageRecording()}"
            android:padding="8dp"
            android:src="@drawable/stop_fill"
            android:contentDescription="@string/content_description_stop_voice_message_recording"
            android:background="@drawable/circle_white_button_background"
            android:visibility="@{viewModel.isVoiceRecordingInProgress ? View.VISIBLE : View.GONE}"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintBottom_toBottomOf="@id/voice_recording_duration"
            app:layout_constraintStart_toStartOf="@id/voice_record_progress"
            app:layout_constraintTop_toTopOf="@id/voice_recording_duration"
            app:tint="?attr/color_main1_500" />

        <ImageView
            android:id="@+id/play_pause_voice_record"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="10dp"
            android:onClick="@{() -> viewModel.togglePlayPauseVoiceRecord()}"
            android:padding="8dp"
            android:src="@{viewModel.isPlayingVoiceRecord ? @drawable/pause_fill : @drawable/play_fill, default=@drawable/play_fill}"
            android:background="@drawable/circle_white_button_background"
            android:contentDescription="@string/content_description_chat_bubble_toggle_voice_message_play_pause"
            android:visibility="@{!viewModel.isVoiceRecordingInProgress ? View.VISIBLE : View.GONE}"
            app:layout_constraintDimensionRatio="1:1"
            app:layout_constraintBottom_toBottomOf="@id/voice_record_duration"
            app:layout_constraintStart_toStartOf="@id/voice_record_progress"
            app:layout_constraintTop_toTopOf="@id/voice_record_duration"
            app:tint="?attr/color_main1_500" />

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/voice_recording_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:text="@{viewModel.formattedVoiceRecordingDuration, default=`00:00`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_600"
            android:background="@drawable/shape_squircle_white_r50_background"
            android:visibility="@{viewModel.isVoiceRecordingInProgress ? View.VISIBLE : View.GONE}"
            android:drawableStart="@drawable/record_fill"
            android:drawablePadding="8dp"
            app:drawableTint="@color/red_danger_500"
            app:layout_constraintBottom_toBottomOf="@id/voice_record_progress"
            app:layout_constraintEnd_toEndOf="@id/voice_record_progress"
            app:layout_constraintTop_toTopOf="@id/voice_record_progress"/>

        <androidx.appcompat.widget.AppCompatTextView
            style="@style/default_text_style"
            android:id="@+id/voice_record_duration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="10dp"
            android:paddingStart="12dp"
            android:paddingEnd="12dp"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:text="@{viewModel.formattedVoiceRecordingDuration, default=`00:00`}"
            android:textSize="14sp"
            android:textColor="?attr/color_main2_600"
            android:background="@drawable/shape_squircle_white_r50_background"
            android:visibility="@{!viewModel.isVoiceRecordingInProgress ? View.VISIBLE : View.GONE}"
            app:layout_constraintBottom_toBottomOf="@id/voice_record_progress"
            app:layout_constraintEnd_toEndOf="@id/voice_record_progress"
            app:layout_constraintTop_toTopOf="@id/voice_record_progress"/>

        <ImageView
            android:id="@+id/voice_recording_send_message"
            android:layout_width="40dp"
            android:layout_height="0dp"
            android:layout_marginEnd="4dp"
            android:onClick="@{() -> viewModel.sendMessage()}"
            android:padding="8dp"
            android:src="@drawable/paper_plane_right"
            android:contentDescription="@string/content_description_chat_send_message"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/icon_primary_color_selector" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>