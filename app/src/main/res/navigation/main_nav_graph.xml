<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_nav_graph"
    app:startDestination="@id/historyListFragment">

    <fragment
        android:id="@+id/contactsListFragment"
        android:name="org.linphone.ui.main.contacts.fragment.ContactsListFragment"
        android:label="ContactsListFragment"
        tools:layout="@layout/contacts_list_fragment">
        <action
            android:id="@+id/action_contactsListFragment_to_historyListFragment"
            app:destination="@id/historyListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/contactsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_contactsListFragment_to_conversationsListFragment"
            app:destination="@id/conversationsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/contactsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_contactsListFragment_to_meetingsListFragment"
            app:destination="@id/meetingsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/contactsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_contactsListFragment_to_newContactFragment"
            app:destination="@id/newContactFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
    </fragment>

    <fragment
        android:id="@+id/historyListFragment"
        android:name="org.linphone.ui.main.history.fragment.HistoryListFragment"
        android:label="HistoryListFragment"
        tools:layout="@layout/history_list_fragment">
        <action
            android:id="@+id/action_historyListFragment_to_contactsListFragment"
            app:destination="@id/contactsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/historyListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_historyListFragment_to_conversationsListFragment"
            app:destination="@id/conversationsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/historyListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_historyListFragment_to_meetingsListFragment"
            app:destination="@id/meetingsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/historyListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_historyListFragment_to_startCallFragment"
            app:destination="@id/startCallFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
        <action
            android:id="@+id/action_historyListFragment_to_meetingWaitingRoomFragment"
            app:destination="@id/meetingWaitingRoomFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
    </fragment>


    <fragment
        android:id="@+id/settingsFragment"
        android:name="org.linphone.ui.main.settings.fragment.SettingsFragment"
        android:label="SettingsFragment"
        tools:layout="@layout/settings_fragment" >
        <action
            android:id="@+id/action_settingsFragment_to_ldapServerConfigurationFragment"
            app:destination="@id/ldapServerConfigurationFragment"
            app:launchSingleTop="true"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingsFragment_to_cardDavAddressBookConfigurationFragment"
            app:destination="@id/cardDavAddressBookConfigurationFragment"
            app:launchSingleTop="true"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingsFragment_to_settingsAdvancedFragment"
            app:destination="@id/settingsAdvancedFragment"
            app:launchSingleTop="true"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <action
            android:id="@+id/action_settingsFragment_to_settingsDeveloperFragment"
            app:destination="@id/settingsDeveloperFragment"
            app:launchSingleTop="true"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
    </fragment>

    <action
        android:id="@+id/action_global_settingsFragment"
        app:destination="@id/settingsFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"
        app:launchSingleTop="true"/>

    <fragment
        android:id="@+id/helpFragment"
        android:name="org.linphone.ui.main.help.fragment.HelpFragment"
        android:label="HelpFragment"
        tools:layout="@layout/help_fragment" >
        <action
            android:id="@+id/action_helpFragment_to_debugFragment"
            app:destination="@id/debugFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right"
            app:launchSingleTop="true" />
    </fragment>

    <action
        android:id="@+id/action_global_helpFragment"
        app:destination="@id/helpFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"
        app:launchSingleTop="true"/>

    <fragment
        android:id="@+id/recordingsListFragment"
        android:name="org.linphone.ui.main.recordings.fragment.RecordingsListFragment"
        android:label="RecordingsListFragment"
        tools:layout="@layout/recordings_list_fragment">

        <action
            android:id="@+id/action_recordingsListFragment_to_recordingMediaPlayerFragment"
            app:destination="@id/recordingMediaPlayerFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right"
            app:launchSingleTop="true" />

    </fragment>

    <action
        android:id="@+id/action_global_recordingsListFragment"
        app:destination="@id/recordingsListFragment"
        app:enterAnim="@anim/slide_in_right"
        app:exitAnim="@anim/slide_out_left"
        app:popEnterAnim="@anim/slide_in_left"
        app:popExitAnim="@anim/slide_out_right"
        app:launchSingleTop="true"/>

    <fragment
        android:id="@+id/startCallFragment"
        android:name="org.linphone.ui.main.history.fragment.StartCallFragment"
        android:label="StartCallFragment"
        tools:layout="@layout/start_call_fragment" />

    <fragment
        android:id="@+id/newContactFragment"
        android:name="org.linphone.ui.main.contacts.fragment.NewContactFragment"
        android:label="NewContactFragment"
        tools:layout="@layout/contact_new_or_edit_fragment"/>


    <fragment
        android:id="@+id/debugFragment"
        android:name="org.linphone.ui.main.help.fragment.DebugFragment"
        android:label="DebugFragment"
        tools:layout="@layout/help_debug_fragment" >
        <action
            android:id="@+id/action_debugFragment_to_conversationsListFragment"
            app:destination="@id/conversationsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/helpFragment"
            app:popUpToInclusive="true"/>
    </fragment>


    <fragment
        android:id="@+id/conversationsListFragment"
        android:name="org.linphone.ui.main.chat.fragment.ConversationsListFragment"
        android:label="ConversationsListFragment"
        tools:layout="@layout/chat_list_fragment" >
        <action
            android:id="@+id/action_conversationsListFragment_to_historyListFragment"
            app:destination="@id/historyListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/conversationsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_conversationsListFragment_to_contactsListFragment"
            app:destination="@id/contactsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/conversationsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_conversationsListFragment_to_meetingsListFragment"
            app:destination="@id/meetingsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/conversationsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_conversationsListFragment_to_startConversationFragment"
            app:destination="@id/startConversationFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
        <action
            android:id="@+id/action_conversationsListFragment_to_meetingWaitingRoomFragment"
            app:destination="@id/meetingWaitingRoomFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
    </fragment>

    <fragment
        android:id="@+id/startConversationFragment"
        android:name="org.linphone.ui.main.chat.fragment.StartConversationFragment"
        android:label="StartConversationFragment"
        tools:layout="@layout/start_chat_fragment"/>

    <fragment
        android:id="@+id/meetingsListFragment"
        android:name="org.linphone.ui.main.meetings.fragment.MeetingsListFragment"
        android:label="MeetingsListFragment"
        tools:layout="@layout/meetings_list_fragment">
        <action
            android:id="@+id/action_meetingsListFragment_to_historyListFragment"
            app:destination="@id/historyListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/meetingsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_meetingsListFragment_to_conversationsListFragment"
            app:destination="@id/conversationsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/meetingsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_meetingsListFragment_to_contactsListFragment"
            app:destination="@id/contactsListFragment"
            app:launchSingleTop="true"
            app:popUpTo="@id/meetingsListFragment"
            app:popUpToInclusive="true" />
        <action
            android:id="@+id/action_meetingsListFragment_to_scheduleMeetingFragment"
            app:destination="@id/scheduleMeetingFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
        <action
            android:id="@+id/action_meetingsListFragment_to_meetingWaitingRoomFragment"
            app:destination="@id/meetingWaitingRoomFragment"
            app:enterAnim="@anim/slide_in"
            app:popExitAnim="@anim/slide_out"
            app:launchSingleTop="true" />
    </fragment>

    <fragment
        android:id="@+id/scheduleMeetingFragment"
        android:name="org.linphone.ui.main.meetings.fragment.ScheduleMeetingFragment"
        android:label="ScheduleMeetingFragment"
        tools:layout="@layout/meeting_schedule_fragment">
        <action
            android:id="@+id/action_scheduleMeetingFragment_to_addParticipantsFragment"
            app:destination="@id/addParticipantsFragment"
            app:enterAnim="@anim/slide_in_right"
            app:exitAnim="@anim/slide_out_left"
            app:popEnterAnim="@anim/slide_in_left"
            app:popExitAnim="@anim/slide_out_right" />
        <argument
            android:name="subject"
            app:argType="string"
            app:nullable="false"
            android:defaultValue="" />
        <argument
            android:name="participants"
            app:argType="string[]"
            app:nullable="true"
            android:defaultValue="@null" />
    </fragment>

    <fragment
        android:id="@+id/addParticipantsFragment"
        android:name="org.linphone.ui.main.fragment.AddParticipantsFragment"
        android:label="AddParticipantsFragment"
        tools:layout="@layout/generic_add_participants_fragment">
        <argument
            android:name="selectedParticipants"
            app:argType="string[]"
            app:nullable="true"
            android:defaultValue="@null" />
    </fragment>

    <action android:id="@+id/action_global_conversationsListFragment"
        app:destination="@id/conversationsListFragment"
        app:launchSingleTop="true"
        app:popUpTo="@id/contactsListFragment"
        app:popUpToInclusive="true"/>

    <fragment
        android:id="@+id/meetingWaitingRoomFragment"
        android:name="org.linphone.ui.main.meetings.fragment.MeetingWaitingRoomFragment"
        android:label="MeetingWaitingRoomFragment"
        tools:layout="@layout/meeting_waiting_room_fragment">
        <argument
            android:name="conferenceUri"
            app:argType="string" />
    </fragment>

    <fragment
        android:id="@+id/ldapServerConfigurationFragment"
        android:name="org.linphone.ui.main.settings.fragment.LdapServerConfigurationFragment"
        android:label="LdapServerConfigurationFragment"
        tools:layout="@layout/settings_contacts_ldap" >
        <argument
            android:name="serverUrl"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/cardDavAddressBookConfigurationFragment"
        android:name="org.linphone.ui.main.settings.fragment.CardDavAddressBookConfigurationFragment"
        android:label="CardDavAddressBookConfigurationFragment"
        tools:layout="@layout/settings_contacts_carddav" >
        <argument
            android:name="displayName"
            app:argType="string"
            app:nullable="true" />
    </fragment>

    <fragment
        android:id="@+id/settingsAdvancedFragment"
        android:name="org.linphone.ui.main.settings.fragment.SettingsAdvancedFragment"
        android:label="SettingsAdvancedFragment"
        tools:layout="@layout/settings_advanced_fragment"/>


    <fragment
        android:id="@+id/recordingMediaPlayerFragment"
        android:name="org.linphone.ui.main.recordings.fragment.RecordingMediaPlayerFragment"
        android:label="RecordingMediaPlayerFragment"
        tools:layout="@layout/recording_player_fragment"/>

    <fragment
        android:id="@+id/settingsDeveloperFragment"
        android:name="org.linphone.ui.main.settings.fragment.SettingsDeveloperFragment"
        android:label="SettingsDeveloperFragment"
        tools:layout="@layout/settings_developer_fragment"/>

</navigation>