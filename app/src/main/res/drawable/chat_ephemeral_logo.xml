<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="131dp"
    android:height="112dp"
    android:viewportWidth="131"
    android:viewportHeight="112">
  <path
      android:pathData="M76.79,21.18V63.55C76.79,64.95 76.23,66.3 75.24,67.29C74.24,68.29 72.9,68.84 71.49,68.84H22.74L16.69,78.15C16.68,78.16 16.66,78.17 16.65,78.18C15.69,78.99 14.49,79.44 13.24,79.44C12.46,79.43 11.7,79.26 10.99,78.93C10.08,78.51 9.3,77.83 8.76,76.98C8.22,76.13 7.94,75.15 7.94,74.14V21.18C7.94,19.78 8.5,18.43 9.49,17.44C10.49,16.45 11.83,15.89 13.24,15.89H71.49C72.9,15.89 74.24,16.45 75.24,17.44C76.23,18.43 76.79,19.78 76.79,21.18Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="121.3"
          android:startY="-19.62"
          android:endX="-31.66"
          android:endY="110.15"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF9730"/>
        <item android:offset="1" android:color="#B2FFB266"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M52.98,42.65C52.79,44.95 51.94,47.14 50.54,48.97C49.13,50.8 47.24,52.19 45.07,52.98C42.91,53.77 40.56,53.93 38.31,53.43C36.06,52.93 34,51.8 32.37,50.17C30.74,48.54 29.6,46.48 29.11,44.23C28.61,41.98 28.76,39.63 29.55,37.47C30.34,35.3 31.74,33.4 33.57,32C35.4,30.6 37.59,29.75 39.89,29.55C40.01,29.54 40.13,29.56 40.25,29.6C40.37,29.63 40.47,29.69 40.57,29.77C40.66,29.85 40.74,29.95 40.79,30.06C40.85,30.17 40.88,30.29 40.89,30.41C40.9,30.53 40.89,30.65 40.85,30.77C40.82,30.89 40.76,30.99 40.68,31.09C40.6,31.18 40.5,31.26 40.39,31.31C40.28,31.37 40.16,31.4 40.04,31.41C38.1,31.58 36.24,32.3 34.69,33.49C33.14,34.67 31.96,36.28 31.29,38.11C30.63,39.94 30.49,41.93 30.92,43.83C31.34,45.74 32.29,47.48 33.67,48.86C35.05,50.24 36.8,51.2 38.7,51.62C40.61,52.04 42.6,51.91 44.43,51.24C46.26,50.58 47.87,49.4 49.05,47.85C50.24,46.3 50.96,44.44 51.12,42.5C51.14,42.25 51.26,42.02 51.45,41.86C51.64,41.7 51.88,41.62 52.13,41.64C52.38,41.66 52.61,41.78 52.77,41.97C52.93,42.16 53,42.4 52.98,42.65ZM39.96,35.13V41.64C39.96,41.89 40.06,42.13 40.24,42.3C40.41,42.48 40.65,42.57 40.89,42.57H47.4C47.65,42.57 47.89,42.48 48.06,42.3C48.24,42.13 48.33,41.89 48.33,41.64C48.33,41.4 48.24,41.16 48.06,40.99C47.89,40.81 47.65,40.71 47.4,40.71H41.82V35.13C41.82,34.89 41.73,34.65 41.55,34.48C41.38,34.3 41.14,34.2 40.89,34.2C40.65,34.2 40.41,34.3 40.24,34.48C40.06,34.65 39.96,34.89 39.96,35.13ZM44.61,32.34C44.89,32.34 45.16,32.26 45.39,32.11C45.62,31.96 45.8,31.74 45.9,31.48C46.01,31.23 46.04,30.95 45.98,30.68C45.93,30.41 45.8,30.16 45.6,29.96C45.41,29.77 45.16,29.63 44.89,29.58C44.62,29.53 44.34,29.55 44.08,29.66C43.83,29.77 43.61,29.94 43.45,30.17C43.3,30.4 43.22,30.67 43.22,30.95C43.22,31.32 43.37,31.67 43.63,31.94C43.89,32.2 44.24,32.34 44.61,32.34ZM48.8,35.13C49.08,35.13 49.34,35.05 49.57,34.9C49.8,34.75 49.98,34.53 50.09,34.27C50.19,34.02 50.22,33.74 50.17,33.47C50.11,33.2 49.98,32.95 49.79,32.75C49.59,32.56 49.34,32.42 49.07,32.37C48.8,32.32 48.52,32.34 48.27,32.45C48.01,32.56 47.79,32.73 47.64,32.96C47.49,33.19 47.4,33.46 47.4,33.74C47.4,34.11 47.55,34.46 47.81,34.73C48.07,34.99 48.43,35.13 48.8,35.13ZM51.59,39.32C51.87,39.32 52.13,39.24 52.36,39.08C52.59,38.93 52.77,38.71 52.88,38.46C52.98,38.2 53.01,37.92 52.96,37.65C52.9,37.38 52.77,37.13 52.58,36.94C52.38,36.74 52.13,36.61 51.86,36.56C51.59,36.5 51.31,36.53 51.06,36.64C50.8,36.74 50.58,36.92 50.43,37.15C50.28,37.38 50.19,37.65 50.19,37.92C50.19,38.29 50.34,38.65 50.6,38.91C50.86,39.17 51.22,39.32 51.59,39.32Z"
      android:fillColor="#FAFEFF"/>
  <path
      android:pathData="M61.37,54.3V96.26C61.37,97.65 61.93,98.99 62.92,99.97C63.92,100.96 65.26,101.51 66.67,101.51H115.42L121.47,110.72C121.49,110.74 121.5,110.75 121.52,110.76C122.47,111.56 123.68,112 124.92,112C125.7,112 126.47,111.83 127.17,111.5C128.09,111.08 128.86,110.41 129.4,109.57C129.94,108.73 130.22,107.75 130.22,106.75V54.3C130.22,52.91 129.66,51.57 128.67,50.59C127.67,49.61 126.33,49.05 124.92,49.05H66.67C65.26,49.05 63.92,49.61 62.92,50.59C61.93,51.57 61.37,52.91 61.37,54.3Z">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="16.86"
          android:startY="13.88"
          android:endX="168.6"
          android:endY="143.86"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF9730"/>
        <item android:offset="1" android:color="#B2FFB266"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M106.98,76.65C106.79,78.95 105.94,81.14 104.54,82.97C103.13,84.8 101.24,86.19 99.07,86.98C96.91,87.77 94.56,87.93 92.31,87.43C90.06,86.93 88,85.8 86.37,84.17C84.74,82.54 83.6,80.48 83.11,78.23C82.61,75.98 82.76,73.63 83.55,71.47C84.34,69.3 85.74,67.4 87.57,66C89.4,64.6 91.59,63.75 93.89,63.55C94.01,63.54 94.13,63.56 94.25,63.6C94.37,63.63 94.47,63.69 94.57,63.77C94.66,63.85 94.74,63.95 94.79,64.06C94.85,64.17 94.88,64.29 94.89,64.41C94.9,64.53 94.89,64.65 94.85,64.77C94.82,64.89 94.76,64.99 94.68,65.09C94.6,65.18 94.5,65.26 94.39,65.31C94.28,65.37 94.16,65.4 94.04,65.41C92.1,65.58 90.24,66.3 88.69,67.49C87.14,68.67 85.96,70.28 85.29,72.11C84.63,73.94 84.49,75.93 84.92,77.83C85.34,79.74 86.29,81.48 87.67,82.86C89.05,84.24 90.8,85.2 92.7,85.62C94.61,86.04 96.6,85.91 98.43,85.24C100.26,84.57 101.87,83.4 103.05,81.85C104.24,80.3 104.96,78.44 105.12,76.5C105.14,76.25 105.26,76.02 105.45,75.86C105.64,75.7 105.88,75.62 106.13,75.64C106.38,75.66 106.61,75.78 106.77,75.97C106.93,76.16 107,76.4 106.98,76.65ZM93.96,69.13V75.64C93.96,75.89 94.06,76.13 94.24,76.3C94.41,76.48 94.65,76.57 94.89,76.57H101.4C101.65,76.57 101.89,76.48 102.06,76.3C102.24,76.13 102.33,75.89 102.33,75.64C102.33,75.4 102.24,75.16 102.06,74.99C101.89,74.81 101.65,74.71 101.4,74.71H95.82V69.13C95.82,68.89 95.73,68.65 95.55,68.48C95.38,68.3 95.14,68.2 94.89,68.2C94.65,68.2 94.41,68.3 94.24,68.48C94.06,68.65 93.96,68.89 93.96,69.13ZM98.61,66.34C98.89,66.34 99.16,66.26 99.39,66.11C99.62,65.96 99.8,65.74 99.9,65.48C100.01,65.23 100.04,64.95 99.98,64.68C99.93,64.41 99.8,64.16 99.6,63.96C99.41,63.77 99.16,63.63 98.89,63.58C98.62,63.53 98.34,63.55 98.08,63.66C97.83,63.77 97.61,63.94 97.45,64.17C97.3,64.4 97.22,64.67 97.22,64.95C97.22,65.32 97.37,65.67 97.63,65.94C97.89,66.2 98.24,66.34 98.61,66.34ZM102.8,69.13C103.07,69.13 103.35,69.05 103.57,68.9C103.8,68.75 103.98,68.53 104.09,68.27C104.19,68.02 104.22,67.74 104.17,67.47C104.11,67.2 103.98,66.95 103.79,66.75C103.59,66.56 103.34,66.42 103.07,66.37C102.8,66.32 102.52,66.34 102.26,66.45C102.01,66.56 101.79,66.73 101.64,66.96C101.49,67.19 101.4,67.46 101.4,67.74C101.4,68.11 101.55,68.46 101.81,68.73C102.07,68.99 102.43,69.13 102.8,69.13ZM105.59,73.32C105.86,73.32 106.14,73.24 106.36,73.08C106.59,72.93 106.77,72.71 106.88,72.46C106.98,72.2 107.01,71.92 106.96,71.65C106.9,71.38 106.77,71.13 106.58,70.94C106.38,70.74 106.13,70.61 105.86,70.56C105.59,70.5 105.31,70.53 105.06,70.64C104.8,70.74 104.58,70.92 104.43,71.15C104.28,71.38 104.19,71.65 104.19,71.92C104.19,72.29 104.34,72.65 104.6,72.91C104.86,73.17 105.22,73.32 105.59,73.32Z"
      android:fillColor="#FAFEFF"/>
</vector>
