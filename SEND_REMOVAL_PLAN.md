
As an AI software engineering agent, your task is to safely and completely remove the file and photo sharing functionality from the Phantom Caller Android application. This feature allows the app to appear as a target in the Android share sheet.

Here is a detailed, step-by-step plan to achieve this. Execute each step carefully.

### Step 1: Remove Intent Filters from `AndroidManifest.xml`

This is the most critical step, as it will remove the app from the Android share sheet.

1.  **Open the file:** `app/src/main/AndroidManifest.xml`
2.  **Locate and delete** the following `<intent-filter>` blocks from within the `<activity android:name="org.linphone.ui.main.MainActivity">` element:

    ```xml
    <intent-filter>
        <action android:name="android.intent.action.SEND" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:mimeType="text/*" />
        <data android:mimeType="image/*" />
        <data android:mimeType="audio/*" />
        <data android:mimeType="video/*" />
        <data android:mimeType="application/*" />
    </intent-filter>

    <intent-filter>
        <action android:name="android.intent.action.SEND_MULTIPLE" />
        <category android:name="android.intent.category.DEFAULT" />
        <data android:mimeType="image/*" />
        <data android:mimeType="audio/*" />
        <data android:mimeType="video/*" />
        <data android:mimeType="application/*" />
    </intent-filter>
    ```

### Step 2: Remove Intent Handling Logic from `MainActivity.kt`

Now that the app can no longer receive `SEND` intents, the code that handles them is obsolete and should be removed.

1.  **Open the file:** `app/src/main/java/org/linphone/ui/main/MainActivity.kt`
2.  **In the `handleIntent` function, delete** the following `when` cases:

    ```kotlin
    Intent.ACTION_SEND -> {
        handleSendIntent(intent, false)
    }
    Intent.ACTION_SEND_MULTIPLE -> {
        handleSendIntent(intent, true)
    }
    ```
3.  **Delete the entire `handleSendIntent` function.** It is no longer called from anywhere.
4.  **Delete the `parseShortcutIfAny` function.** It is only used by `handleSendIntent`.
5.  **In the `onCreate` method, delete** the following observer blocks, as they are related to the sharing functionality:

    ```kotlin
    viewModel.clearFilesOrTextPendingSharingEvent.observe(this) {
        it.consume {
            sharedViewModel.filesToShareFromIntent.value = arrayListOf<String>()
            sharedViewModel.textToShareFromIntent.value = ""
        }
    }

    sharedViewModel.filesToShareFromIntent.observe(this) { list ->
        if (list.isNotEmpty()) {
            viewModel.addFilesPendingSharing(list)
        } else {
            viewModel.filesOrTextPendingSharingListCleared()
        }
    }

    sharedViewModel.textToShareFromIntent.observe(this) { text ->
        if (!text.isEmpty()) {
            viewModel.addTextPendingSharing()
        } else {
            viewModel.filesOrTextPendingSharingListCleared()
        }
    }
    ```

### Step 3: Clean up ViewModels

Remove the now-unused properties and functions from the `MainViewModel` and `SharedMainViewModel`.

1.  **Open the file for `SharedMainViewModel`**. You will need to find it first, it should be in `app/src/main/java/org/linphone/ui/main/viewmodel/`.
2.  **Delete** the following properties:

    ```kotlin
    val filesToShareFromIntent = MutableLiveData<ArrayList<String>>()
    val textToShareFromIntent = MutableLiveData<String>()
    ```

3.  **Open the file for `MainViewModel`**. It should be in the same directory.
4.  **Delete** the following properties and functions:

    *   `val clearFilesOrTextPendingSharingEvent`
    *   `fun addFilesPendingSharing(list: ArrayList<String>)`
    *   `fun addTextPendingSharing()`
    *   `fun filesOrTextPendingSharingListCleared()`

After completing these steps, the file sharing functionality will be completely and safely removed from the application. Verify your changes by building the application to ensure there are no compilation errors.
